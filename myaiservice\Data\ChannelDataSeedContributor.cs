using myaiservice.Entities;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Data
{
    /// <summary>
    /// 渠道数据种子贡献者
    /// </summary>
    public class ChannelDataSeedContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Channel, Guid> _channelRepository;

        public ChannelDataSeedContributor(IRepository<Channel, Guid> channelRepository)
        {
            _channelRepository = channelRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _channelRepository.GetCountAsync() > 0)
            {
                return; // 已有数据，跳过种子数据
            }

            var channels = new List<Channel>
            {
                // OpenAI 官方渠道
                new Channel(Guid.NewGuid(), "OpenAI Official", ChannelType.OpenAI, "https://api.openai.com", "your-openai-api-key")
                {
                    Priority = 1,
                    Weight = 10,
                    SupportedModels = "[\"gpt-4\", \"gpt-4-turbo\", \"gpt-3.5-turbo\", \"text-embedding-3-large\", \"dall-e-3\"]",
                    Group = "openai",
                    MaxConcurrency = 20,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 100.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "OpenAI官方API渠道"
                },

                // Azure OpenAI 渠道
                new Channel(Guid.NewGuid(), "Azure OpenAI", ChannelType.AzureOpenAI, "https://your-resource.openai.azure.com", "your-azure-api-key")
                {
                    Priority = 2,
                    Weight = 8,
                    SupportedModels = "[\"gpt-4\", \"gpt-35-turbo\", \"text-embedding-ada-002\"]",
                    Group = "azure",
                    MaxConcurrency = 15,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 50.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "Azure OpenAI服务渠道"
                },

                // Anthropic Claude 渠道
                new Channel(Guid.NewGuid(), "Anthropic Claude", ChannelType.Claude, "https://api.anthropic.com", "your-anthropic-api-key")
                {
                    Priority = 1,
                    Weight = 9,
                    SupportedModels = "[\"claude-3-opus-20240229\", \"claude-3-sonnet-20240229\", \"claude-3-haiku-20240307\"]",
                    Group = "anthropic",
                    MaxConcurrency = 10,
                    TimeoutSeconds = 90,
                    RetryCount = 3,
                    Balance = 75.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "Anthropic Claude API渠道"
                },

                // Google Gemini 渠道
                new Channel(Guid.NewGuid(), "Google Gemini", ChannelType.Gemini, "https://generativelanguage.googleapis.com", "your-google-api-key")
                {
                    Priority = 2,
                    Weight = 7,
                    SupportedModels = "[\"gemini-pro\", \"gemini-pro-vision\"]",
                    Group = "google",
                    MaxConcurrency = 8,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 30.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "Google Gemini API渠道"
                },

                // 百度文心一言渠道
                new Channel(Guid.NewGuid(), "百度文心一言", ChannelType.BaiduWenxin, "https://aip.baidubce.com", "your-baidu-api-key")
                {
                    Priority = 3,
                    Weight = 6,
                    SupportedModels = "[\"ernie-bot-turbo\", \"ernie-bot-4\"]",
                    Group = "baidu",
                    MaxConcurrency = 10,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 20.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "百度文心一言API渠道"
                },

                // 讯飞星火渠道
                new Channel(Guid.NewGuid(), "讯飞星火", ChannelType.XunfeiSpark, "https://spark-api.xf-yun.com", "your-xunfei-api-key")
                {
                    Priority = 3,
                    Weight = 5,
                    SupportedModels = "[\"spark-3.0\", \"spark-2.0\"]",
                    Group = "xunfei",
                    MaxConcurrency = 8,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 15.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "科大讯飞星火API渠道"
                },

                // 智谱GLM渠道
                new Channel(Guid.NewGuid(), "智谱GLM", ChannelType.ZhipuGLM, "https://open.bigmodel.cn", "your-zhipu-api-key")
                {
                    Priority = 3,
                    Weight = 6,
                    SupportedModels = "[\"glm-4\", \"glm-3-turbo\"]",
                    Group = "zhipu",
                    MaxConcurrency = 10,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 25.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "智谱AI GLM API渠道"
                },

                // Moonshot渠道
                new Channel(Guid.NewGuid(), "Moonshot AI", ChannelType.MoonshotAI, "https://api.moonshot.cn", "your-moonshot-api-key")
                {
                    Priority = 3,
                    Weight = 5,
                    SupportedModels = "[\"moonshot-v1-8k\", \"moonshot-v1-32k\", \"moonshot-v1-128k\"]",
                    Group = "moonshot",
                    MaxConcurrency = 8,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 20.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "月之暗面Kimi API渠道"
                },

                // DeepSeek渠道
                new Channel(Guid.NewGuid(), "DeepSeek", ChannelType.DeepSeek, "https://api.deepseek.com", "your-deepseek-api-key")
                {
                    Priority = 3,
                    Weight = 7,
                    SupportedModels = "[\"deepseek-chat\", \"deepseek-coder\"]",
                    Group = "deepseek",
                    MaxConcurrency = 12,
                    TimeoutSeconds = 60,
                    RetryCount = 3,
                    Balance = 30.00m,
                    AutoCheckBalance = true,
                    BalanceCheckInterval = 60,
                    MaxFailures = 5,
                    Remarks = "DeepSeek深度求索API渠道"
                }
            };

            // 设置所有渠道为禁用状态，需要管理员手动配置API密钥后启用
            foreach (var channel in channels)
            {
                channel.Status = ChannelStatus.Disabled;
            }

            await _channelRepository.InsertManyAsync(channels);
        }
    }
}
