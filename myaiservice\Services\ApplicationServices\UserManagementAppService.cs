using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Users;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 用户管理应用服务
    /// </summary>
    [Authorize]
    public class UserManagementAppService : ApplicationService
    {
        private readonly IRepository<UserProfile, Guid> _userProfileRepository;
        private readonly IRepository<InvitationCode, Guid> _invitationCodeRepository;
        private readonly QuotaDomainService _quotaDomainService;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IdentityUserManager _identityUserManager;

        public UserManagementAppService(
            IRepository<UserProfile, Guid> userProfileRepository,
            IRepository<InvitationCode, Guid> invitationCodeRepository,
            QuotaDomainService quotaDomainService,
            IIdentityUserRepository identityUserRepository,
            IdentityUserManager identityUserManager)
        {
            _userProfileRepository = userProfileRepository;
            _invitationCodeRepository = invitationCodeRepository;
            _quotaDomainService = quotaDomainService;
            _identityUserRepository = identityUserRepository;
            _identityUserManager = identityUserManager;
        }

        /// <summary>
        /// 创建用户资料
        /// </summary>
        public async Task<UserProfileDto> CreateUserProfileAsync(CreateUserProfileDto input)
        {
            // 检查用户是否已存在资料
            var existingProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == input.UserId);
            if (existingProfile != null)
                throw new InvalidOperationException("User profile already exists");

            var userProfile = new UserProfile(GuidGenerator.Create(), input.UserId)
            {
                UserGroup = input.UserGroup,
                RequestLimitPerMinute = input.RequestLimitPerMinute ?? 60,
                RequestLimitPerHour = input.RequestLimitPerHour ?? 3600,
                RequestLimitPerDay = input.RequestLimitPerDay ?? 86400,
                Remarks = input.Remarks
            };

            await _userProfileRepository.InsertAsync(userProfile);

            // 如果有初始配额，创建配额记录
            if (input.InitialQuota > 0)
            {
                await _quotaDomainService.CreateQuotaAsync(
                    input.UserId, 
                    QuotaType.General, 
                    input.InitialQuota, 
                    QuotaSource.SystemGrant, 
                    null, 
                    "Initial quota grant");
            }

            return ObjectMapper.Map<UserProfile, UserProfileDto>(userProfile);
        }

        /// <summary>
        /// 获取用户资料
        /// </summary>
        public async Task<UserProfileDto> GetUserProfileAsync(Guid userId)
        {
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == userId);
            if (userProfile == null)
            {
                // 如果用户资料不存在，自动创建
                userProfile = new UserProfile(GuidGenerator.Create(), userId);
                await _userProfileRepository.InsertAsync(userProfile);
            }

            return ObjectMapper.Map<UserProfile, UserProfileDto>(userProfile);
        }

        /// <summary>
        /// 更新用户资料
        /// </summary>
        public async Task<UserProfileDto> UpdateUserProfileAsync(Guid userId, UpdateUserProfileDto input)
        {
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == userId);
            if (userProfile == null)
                throw new InvalidOperationException("User profile not found");

            userProfile.UserGroup = input.UserGroup;
            userProfile.RequestLimitPerMinute = input.RequestLimitPerMinute ?? userProfile.RequestLimitPerMinute;
            userProfile.RequestLimitPerHour = input.RequestLimitPerHour ?? userProfile.RequestLimitPerHour;
            userProfile.RequestLimitPerDay = input.RequestLimitPerDay ?? userProfile.RequestLimitPerDay;
            userProfile.IpWhitelist = input.IpWhitelist;
            userProfile.AllowedModels = input.AllowedModels;
            userProfile.Remarks = input.Remarks;

            await _userProfileRepository.UpdateAsync(userProfile);

            return ObjectMapper.Map<UserProfile, UserProfileDto>(userProfile);
        }

        /// <summary>
        /// 生成邀请码
        /// </summary>
        public async Task<InvitationCodeDto> GenerateInvitationCodeAsync(GenerateInvitationCodeDto input)
        {
            var currentUserId = CurrentUser.Id.Value;
            
            // 生成唯一邀请码
            string code;
            do
            {
                code = GenerateRandomCode();
            } while (await _invitationCodeRepository.AnyAsync(ic => ic.Code == code));

            var invitationCode = new InvitationCode(
                GuidGenerator.Create(), 
                code, 
                currentUserId, 
                input.RewardAmount, 
                input.ExpiresAt)
            {
                Remarks = input.Remarks
            };

            await _invitationCodeRepository.InsertAsync(invitationCode);

            return ObjectMapper.Map<InvitationCode, InvitationCodeDto>(invitationCode);
        }

        /// <summary>
        /// 使用邀请码
        /// </summary>
        public async Task<bool> UseInvitationCodeAsync(string code)
        {
            var currentUserId = CurrentUser.Id.Value;
            
            var invitationCode = await _invitationCodeRepository.FirstOrDefaultAsync(ic => ic.Code == code);
            if (invitationCode == null || invitationCode.IsUsed || invitationCode.IsExpired())
                return false;

            // 不能使用自己的邀请码
            if (invitationCode.CreatedByUserId == currentUserId)
                return false;

            // 使用邀请码
            if (!invitationCode.Use(currentUserId))
                return false;

            await _invitationCodeRepository.UpdateAsync(invitationCode);

            // 为邀请人和被邀请人发放奖励
            if (invitationCode.RewardAmount > 0)
            {
                // 邀请人奖励
                await _quotaDomainService.RechargeUserQuotaAsync(
                    invitationCode.CreatedByUserId, 
                    QuotaType.General, 
                    invitationCode.RewardAmount, 
                    QuotaSource.InvitationReward, 
                    null, 
                    $"Invitation reward for inviting user");

                // 被邀请人奖励 (通常是邀请人奖励的一半)
                var inviteeReward = invitationCode.RewardAmount * 0.5m;
                await _quotaDomainService.RechargeUserQuotaAsync(
                    currentUserId, 
                    QuotaType.General, 
                    inviteeReward, 
                    QuotaSource.InvitationReward, 
                    null, 
                    $"Welcome bonus for using invitation code {code}");

                // 更新邀请人的统计信息
                var inviterProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == invitationCode.CreatedByUserId);
                if (inviterProfile != null)
                {
                    inviterProfile.RecordInvitation(invitationCode.RewardAmount);
                    await _userProfileRepository.UpdateAsync(inviterProfile);
                }
            }

            // 更新被邀请人的资料
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == currentUserId);
            if (userProfile != null)
            {
                userProfile.InvitedBy = invitationCode.CreatedByUserId;
                userProfile.InvitedAt = DateTime.UtcNow;
                userProfile.InvitationCode = code;
                await _userProfileRepository.UpdateAsync(userProfile);
            }

            return true;
        }

        /// <summary>
        /// 获取用户的邀请码列表
        /// </summary>
        public async Task<List<InvitationCodeDto>> GetUserInvitationCodesAsync()
        {
            var currentUserId = CurrentUser.Id.Value;
            var invitationCodes = await _invitationCodeRepository.GetListAsync(ic => ic.CreatedByUserId == currentUserId);
            
            return ObjectMapper.Map<List<InvitationCode>, List<InvitationCodeDto>>(invitationCodes);
        }

        /// <summary>
        /// 记录用户登录
        /// </summary>
        public async Task RecordUserLoginAsync(string? ipAddress)
        {
            var currentUserId = CurrentUser.Id.Value;
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == currentUserId);
            
            if (userProfile != null)
            {
                userProfile.RecordLogin(ipAddress);
                await _userProfileRepository.UpdateAsync(userProfile);
            }
        }

        /// <summary>
        /// 设置用户状态
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task SetUserStatusAsync(Guid userId, UserStatus status)
        {
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == userId);
            if (userProfile == null)
                throw new InvalidOperationException("User profile not found");

            switch (status)
            {
                case UserStatus.Active:
                    userProfile.Activate();
                    break;
                case UserStatus.Disabled:
                    userProfile.Disable();
                    break;
                case UserStatus.Suspended:
                    userProfile.Suspend();
                    break;
            }

            await _userProfileRepository.UpdateAsync(userProfile);
        }

        /// <summary>
        /// 为用户充值配额
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task RechargeUserQuotaAsync(Guid userId, decimal amount, string? description = null)
        {
            await _quotaDomainService.RechargeUserQuotaAsync(
                userId, 
                QuotaType.General, 
                amount, 
                QuotaSource.AdminAllocation, 
                null, 
                description ?? "Admin recharge");
        }

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        public async Task<UserStatisticsDto> GetUserStatisticsAsync(Guid userId)
        {
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == userId);
            if (userProfile == null)
                throw new InvalidOperationException("User profile not found");

            var quotaStatistics = await _quotaDomainService.GetUserQuotaStatisticsAsync(userId);

            return new UserStatisticsDto
            {
                UserId = userId,
                TotalRequests = userProfile.TotalRequests,
                SuccessfulRequests = userProfile.SuccessfulRequests,
                SuccessRate = userProfile.GetSuccessRate(),
                TotalTokens = userProfile.TotalTokens,
                TotalCost = userProfile.TotalCost,
                RemainingQuota = userProfile.RemainingQuota,
                QuotaUsageRate = userProfile.GetQuotaUsageRate(),
                InvitedUserCount = userProfile.InvitedUserCount,
                InvitationRewards = userProfile.InvitationRewards,
                LastLoginTime = userProfile.LastLoginTime,
                LoginCount = userProfile.LoginCount,
                QuotaDetails = quotaStatistics.QuotaDetails
            };
        }

        /// <summary>
        /// 生成随机邀请码
        /// </summary>
        private string GenerateRandomCode()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }

    // DTOs
    public class CreateUserProfileDto
    {
        public Guid UserId { get; set; }
        public string? UserGroup { get; set; }
        public int? RequestLimitPerMinute { get; set; }
        public int? RequestLimitPerHour { get; set; }
        public int? RequestLimitPerDay { get; set; }
        public decimal InitialQuota { get; set; } = 0;
        public string? Remarks { get; set; }
    }

    public class UpdateUserProfileDto
    {
        public string? UserGroup { get; set; }
        public int? RequestLimitPerMinute { get; set; }
        public int? RequestLimitPerHour { get; set; }
        public int? RequestLimitPerDay { get; set; }
        public string? IpWhitelist { get; set; }
        public string? AllowedModels { get; set; }
        public string? Remarks { get; set; }
    }

    public class GenerateInvitationCodeDto
    {
        [Required]
        public decimal RewardAmount { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? Remarks { get; set; }
    }

    public class UserProfileDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public UserStatus Status { get; set; }
        public string? UserGroup { get; set; }
        public string? InvitationCode { get; set; }
        public Guid? InvitedBy { get; set; }
        public DateTime? InvitedAt { get; set; }
        public decimal TotalQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public decimal RemainingQuota { get; set; }
        public bool IsUnlimitedQuota { get; set; }
        public int RequestLimitPerMinute { get; set; }
        public int RequestLimitPerHour { get; set; }
        public int RequestLimitPerDay { get; set; }
        public string? IpWhitelist { get; set; }
        public string? AllowedModels { get; set; }
        public DateTime? LastLoginTime { get; set; }
        public string? LastLoginIp { get; set; }
        public int LoginCount { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public int InvitedUserCount { get; set; }
        public decimal InvitationRewards { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class InvitationCodeDto
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public Guid CreatedByUserId { get; set; }
        public Guid? UsedByUserId { get; set; }
        public bool IsUsed { get; set; }
        public DateTime? UsedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public decimal RewardAmount { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class UserStatisticsDto
    {
        public Guid UserId { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public decimal RemainingQuota { get; set; }
        public double QuotaUsageRate { get; set; }
        public int InvitedUserCount { get; set; }
        public decimal InvitationRewards { get; set; }
        public DateTime? LastLoginTime { get; set; }
        public int LoginCount { get; set; }
        public List<QuotaDetail> QuotaDetails { get; set; } = new();
    }
}
