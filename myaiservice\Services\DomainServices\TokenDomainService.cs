using myaiservice.Entities;
using myaiservice.Entities.ValueObjects;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using System.Security.Cryptography;
using System.Text;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// Token领域服务
    /// </summary>
    public class TokenDomainService : DomainService
    {
        private readonly IRepository<Token, Guid> _tokenRepository;
        private readonly IRepository<UserProfile, Guid> _userProfileRepository;

        public TokenDomainService(
            IRepository<Token, Guid> tokenRepository,
            IRepository<UserProfile, Guid> userProfileRepository)
        {
            _tokenRepository = tokenRepository;
            _userProfileRepository = userProfileRepository;
        }

        /// <summary>
        /// 生成48位安全Token
        /// </summary>
        public string GenerateSecureToken()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var result = new StringBuilder(48);

            for (int i = 0; i < 48; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }

            return result.ToString();
        }

        /// <summary>
        /// 创建新Token
        /// </summary>
        public async Task<Token> CreateTokenAsync(string name, Guid userId, TokenType type = TokenType.ApiAccess)
        {
            // 生成唯一Token值
            string tokenValue;
            do
            {
                tokenValue = GenerateSecureToken();
            } while (await _tokenRepository.AnyAsync(t => t.Value == tokenValue));

            var token = new Token(GuidGenerator.Create(), name, tokenValue, userId, type);
            
            return token;
        }

        /// <summary>
        /// 验证Token是否有效
        /// </summary>
        public async Task<(bool IsValid, string? ErrorMessage)> ValidateTokenAsync(string tokenValue, string? ipAddress = null, string? modelName = null)
        {
            var token = await _tokenRepository.FirstOrDefaultAsync(t => t.Value == tokenValue);
            if (token == null)
                return (false, "Invalid token");

            if (!token.IsEnabled)
                return (false, "Token is disabled");

            if (token.IsExpired())
                return (false, "Token has expired");

            if (!string.IsNullOrEmpty(ipAddress) && !token.IsIpAllowed(ipAddress))
                return (false, "IP address not allowed");

            if (!string.IsNullOrEmpty(modelName) && !token.IsModelAllowed(modelName))
                return (false, "Model not allowed");

            return (true, null);
        }

        /// <summary>
        /// 检查Token配额是否足够
        /// </summary>
        public async Task<(bool HasSufficient, decimal Available)> CheckQuotaAsync(Guid tokenId, decimal requiredQuota)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            
            if (token.IsUnlimited)
                return (true, decimal.MaxValue);

            return (token.HasSufficientQuota(requiredQuota), token.RemainingQuota);
        }

        /// <summary>
        /// 消费Token配额
        /// </summary>
        public async Task<bool> ConsumeQuotaAsync(Guid tokenId, decimal amount, TokenUsage tokenUsage, string? ipAddress = null)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            
            if (!token.ConsumeQuota(amount))
                return false;

            token.RecordUsage(ipAddress, tokenUsage.InputTokens, tokenUsage.OutputTokens, amount);
            
            await _tokenRepository.UpdateAsync(token);
            return true;
        }

        /// <summary>
        /// 重置Token统计数据
        /// </summary>
        public async Task ResetTokenStatisticsAsync(Guid tokenId)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            token.ResetStatistics();
            await _tokenRepository.UpdateAsync(token);
        }

        /// <summary>
        /// 为Token添加配额
        /// </summary>
        public async Task AddQuotaAsync(Guid tokenId, decimal amount)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            token.AddQuota(amount);
            await _tokenRepository.UpdateAsync(token);
        }

        /// <summary>
        /// 设置Token过期时间
        /// </summary>
        public async Task SetExpirationAsync(Guid tokenId, DateTime? expiresAt)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            token.ExpiresAt = expiresAt;
            await _tokenRepository.UpdateAsync(token);
        }

        /// <summary>
        /// 启用/禁用Token
        /// </summary>
        public async Task SetTokenStatusAsync(Guid tokenId, bool isEnabled)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            token.IsEnabled = isEnabled;
            await _tokenRepository.UpdateAsync(token);
        }

        /// <summary>
        /// 获取用户的所有Token
        /// </summary>
        public async Task<List<Token>> GetUserTokensAsync(Guid userId)
        {
            return await _tokenRepository.GetListAsync(t => t.UserId == userId);
        }

        /// <summary>
        /// 获取Token使用统计
        /// </summary>
        public async Task<TokenStatistics> GetTokenStatisticsAsync(Guid tokenId)
        {
            var token = await _tokenRepository.GetAsync(tokenId);
            
            return new TokenStatistics
            {
                TokenId = tokenId,
                TotalRequests = token.TotalRequests,
                SuccessfulRequests = token.SuccessfulRequests,
                SuccessRate = token.GetSuccessRate(),
                TotalTokens = token.TotalTokens,
                InputTokens = token.InputTokens,
                OutputTokens = token.OutputTokens,
                RemainingQuota = token.RemainingQuota,
                UsedQuota = token.UsedQuota,
                LastUsedTime = token.LastUsedTime
            };
        }

        /// <summary>
        /// 批量禁用过期Token
        /// </summary>
        public async Task DisableExpiredTokensAsync()
        {
            var expiredTokens = await _tokenRepository.GetListAsync(t => 
                t.IsEnabled && 
                t.ExpiresAt.HasValue && 
                t.ExpiresAt.Value <= DateTime.UtcNow);

            foreach (var token in expiredTokens)
            {
                token.IsEnabled = false;
            }

            await _tokenRepository.UpdateManyAsync(expiredTokens);
        }
    }

    /// <summary>
    /// Token统计信息
    /// </summary>
    public class TokenStatistics
    {
        public Guid TokenId { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public long TotalTokens { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public decimal RemainingQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public DateTime? LastUsedTime { get; set; }
    }
}
