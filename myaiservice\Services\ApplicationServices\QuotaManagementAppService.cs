using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 配额管理应用服务
    /// </summary>
    [Authorize]
    public class QuotaManagementAppService : ApplicationService
    {
        private readonly IRepository<Quota, Guid> _quotaRepository;
        private readonly IRepository<QuotaTransaction, Guid> _quotaTransactionRepository;
        private readonly QuotaDomainService _quotaDomainService;
        private readonly BillingService _billingService;

        public QuotaManagementAppService(
            IRepository<Quota, Guid> quotaRepository,
            IRepository<QuotaTransaction, Guid> quotaTransactionRepository,
            QuotaDomainService quotaDomainService,
            BillingService billingService)
        {
            _quotaRepository = quotaRepository;
            _quotaTransactionRepository = quotaTransactionRepository;
            _quotaDomainService = quotaDomainService;
            _billingService = billingService;
        }

        /// <summary>
        /// 获取当前用户配额信息
        /// </summary>
        public async Task<UserQuotaStatistics> GetMyQuotaAsync()
        {
            var userId = CurrentUser.Id!.Value;
            return await _quotaDomainService.GetUserQuotaStatisticsAsync(userId);
        }

        /// <summary>
        /// 获取指定用户配额信息
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<UserQuotaStatistics> GetUserQuotaAsync(Guid userId)
        {
            return await _quotaDomainService.GetUserQuotaStatisticsAsync(userId);
        }

        /// <summary>
        /// 获取当前用户配额交易记录
        /// </summary>
        public async Task<List<QuotaTransactionDto>> GetMyQuotaTransactionsAsync(int maxResultCount = 50, int skipCount = 0)
        {
            var userId = CurrentUser.Id!.Value;
            var queryable = await _quotaTransactionRepository.GetQueryableAsync();
            var transactions = await AsyncExecuter.ToListAsync(
                queryable.Where(t => t.UserId == userId)
                         .OrderByDescending(t => t.CreationTime)
                         .Skip(skipCount)
                         .Take(maxResultCount));

            return ObjectMapper.Map<List<QuotaTransaction>, List<QuotaTransactionDto>>(transactions);
        }

        /// <summary>
        /// 获取指定用户配额交易记录
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<QuotaTransactionDto>> GetUserQuotaTransactionsAsync(Guid userId, int maxResultCount = 50, int skipCount = 0)
        {
            var queryable = await _quotaTransactionRepository.GetQueryableAsync();
            var transactions = await AsyncExecuter.ToListAsync(
                queryable.Where(t => t.UserId == userId)
                         .OrderByDescending(t => t.CreationTime)
                         .Skip(skipCount)
                         .Take(maxResultCount));

            return ObjectMapper.Map<List<QuotaTransaction>, List<QuotaTransactionDto>>(transactions);
        }

        /// <summary>
        /// 为用户充值配额
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<QuotaDto> RechargeUserQuotaAsync(RechargeQuotaDto input)
        {
            var quota = await _quotaDomainService.RechargeUserQuotaAsync(
                input.UserId,
                input.QuotaType,
                input.Amount,
                input.Source,
                input.ExpiresAt,
                input.Description);

            return ObjectMapper.Map<Quota, QuotaDto>(quota);
        }

        /// <summary>
        /// 创建配额
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<QuotaDto> CreateQuotaAsync(CreateQuotaDto input)
        {
            var quota = await _quotaDomainService.CreateQuotaAsync(
                input.UserId,
                input.QuotaType,
                input.TotalQuota,
                input.Source,
                input.ExpiresAt,
                input.Description);

            return ObjectMapper.Map<Quota, QuotaDto>(quota);
        }

        /// <summary>
        /// 管理员调整配额
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task AdminAdjustQuotaAsync(AdminAdjustQuotaDto input)
        {
            await _quotaDomainService.AdminAdjustQuotaAsync(
                input.UserId,
                input.QuotaType,
                input.NewTotalQuota,
                input.Description);
        }

        /// <summary>
        /// 退款配额
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<bool> RefundQuotaAsync(RefundQuotaDto input)
        {
            return await _billingService.ProcessRefundAsync(
                input.UserId,
                input.Amount,
                input.Reason,
                input.RelatedTransactionId);
        }

        /// <summary>
        /// 获取用户计费统计
        /// </summary>
        public async Task<UserBillingStatistics> GetMyBillingStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var userId = CurrentUser.Id!.Value;
            return await _billingService.GetUserBillingStatisticsAsync(userId, startDate, endDate);
        }

        /// <summary>
        /// 获取指定用户计费统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<UserBillingStatistics> GetUserBillingStatisticsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _billingService.GetUserBillingStatisticsAsync(userId, startDate, endDate);
        }

        /// <summary>
        /// 获取模型计费统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<ModelBillingStatistics>> GetModelBillingStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _billingService.GetModelBillingStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 预估请求费用
        /// </summary>
        public async Task<decimal> EstimateRequestCostAsync(string modelName, int estimatedInputTokens, int estimatedOutputTokens)
        {
            return await _billingService.EstimateRequestCostAsync(modelName, estimatedInputTokens, estimatedOutputTokens);
        }

        /// <summary>
        /// 检查配额是否足够
        /// </summary>
        public async Task<QuotaCheckResult> CheckQuotaAsync(string modelName, int estimatedInputTokens, int estimatedOutputTokens)
        {
            var userId = CurrentUser.Id!.Value;
            var estimatedCost = await _billingService.EstimateRequestCostAsync(modelName, estimatedInputTokens, estimatedOutputTokens);
            var (hasSufficient, available) = await _quotaDomainService.CheckUserQuotaAsync(userId, QuotaType.General, estimatedCost);

            return new QuotaCheckResult
            {
                HasSufficientQuota = hasSufficient,
                AvailableQuota = available,
                RequiredQuota = estimatedCost,
                ModelName = modelName,
                EstimatedInputTokens = estimatedInputTokens,
                EstimatedOutputTokens = estimatedOutputTokens
            };
        }

        /// <summary>
        /// 获取配额使用趋势
        /// </summary>
        public async Task<List<QuotaUsageTrend>> GetMyQuotaUsageTrendAsync(int days = 30)
        {
            var userId = CurrentUser.Id!.Value;
            var startDate = DateTime.UtcNow.Date.AddDays(-days);
            var transactions = await _quotaTransactionRepository.GetListAsync(t => 
                t.UserId == userId && 
                t.Type == QuotaTransactionType.Consumption &&
                t.CreationTime >= startDate);

            return transactions
                .GroupBy(t => t.CreationTime.Date)
                .Select(g => new QuotaUsageTrend
                {
                    Date = g.Key,
                    TotalUsage = g.Sum(t => t.Amount),
                    TransactionCount = g.Count(),
                    AverageUsage = g.Average(t => t.Amount)
                })
                .OrderBy(t => t.Date)
                .ToList();
        }

        /// <summary>
        /// 获取配额预警信息
        /// </summary>
        public async Task<List<QuotaAlert>> GetQuotaAlertsAsync()
        {
            var userId = CurrentUser.Id!.Value;
            var quotaStats = await _quotaDomainService.GetUserQuotaStatisticsAsync(userId);
            var alerts = new List<QuotaAlert>();

            foreach (var quota in quotaStats.QuotaDetails)
            {
                var usageRate = quota.TotalQuota > 0 ? (double)(quota.UsedQuota / quota.TotalQuota) * 100 : 0;

                if (usageRate >= 90)
                {
                    alerts.Add(new QuotaAlert
                    {
                        Type = QuotaAlertType.Critical,
                        QuotaType = quota.Type,
                        Message = $"{quota.Type} quota is {usageRate:F1}% used",
                        UsageRate = usageRate,
                        RemainingQuota = quota.RemainingQuota
                    });
                }
                else if (usageRate >= 80)
                {
                    alerts.Add(new QuotaAlert
                    {
                        Type = QuotaAlertType.Warning,
                        QuotaType = quota.Type,
                        Message = $"{quota.Type} quota is {usageRate:F1}% used",
                        UsageRate = usageRate,
                        RemainingQuota = quota.RemainingQuota
                    });
                }

                if (quota.ExpiringQuota > 0)
                {
                    alerts.Add(new QuotaAlert
                    {
                        Type = QuotaAlertType.Expiring,
                        QuotaType = quota.Type,
                        Message = $"{quota.ExpiringQuota:F4} {quota.Type} quota will expire within 7 days",
                        RemainingQuota = quota.ExpiringQuota
                    });
                }
            }

            return alerts;
        }
    }

    // DTOs
    public class RechargeQuotaDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        public QuotaType QuotaType { get; set; } = QuotaType.General;

        [Required]
        [Range(0.0001, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public QuotaSource Source { get; set; } = QuotaSource.AdminAllocation;

        public DateTime? ExpiresAt { get; set; }

        public string? Description { get; set; }
    }

    public class CreateQuotaDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        public QuotaType QuotaType { get; set; } = QuotaType.General;

        [Required]
        [Range(0.0001, double.MaxValue)]
        public decimal TotalQuota { get; set; }

        [Required]
        public QuotaSource Source { get; set; } = QuotaSource.AdminAllocation;

        public DateTime? ExpiresAt { get; set; }

        public string? Description { get; set; }
    }

    public class AdminAdjustQuotaDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        public QuotaType QuotaType { get; set; } = QuotaType.General;

        [Required]
        [Range(0, double.MaxValue)]
        public decimal NewTotalQuota { get; set; }

        public string? Description { get; set; }
    }

    public class RefundQuotaDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        [Range(0.0001, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public string Reason { get; set; } = string.Empty;

        public Guid? RelatedTransactionId { get; set; }
    }

    public class QuotaCheckResult
    {
        public bool HasSufficientQuota { get; set; }
        public decimal AvailableQuota { get; set; }
        public decimal RequiredQuota { get; set; }
        public string ModelName { get; set; } = string.Empty;
        public int EstimatedInputTokens { get; set; }
        public int EstimatedOutputTokens { get; set; }
    }

    public class QuotaUsageTrend
    {
        public DateTime Date { get; set; }
        public decimal TotalUsage { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageUsage { get; set; }
    }

    public class QuotaAlert
    {
        public QuotaAlertType Type { get; set; }
        public QuotaType QuotaType { get; set; }
        public string Message { get; set; } = string.Empty;
        public double UsageRate { get; set; }
        public decimal RemainingQuota { get; set; }
    }

    public enum QuotaAlertType
    {
        Warning = 1,
        Critical = 2,
        Expiring = 3
    }
}
