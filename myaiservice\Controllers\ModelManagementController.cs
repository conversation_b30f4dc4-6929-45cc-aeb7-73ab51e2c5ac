using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Entities;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 模型管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ModelManagementController : AbpControllerBase
    {
        private readonly ModelManagementAppService _modelManagementAppService;

        public ModelManagementController(ModelManagementAppService modelManagementAppService)
        {
            _modelManagementAppService = modelManagementAppService;
        }

        /// <summary>
        /// 获取所有可用模型
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsAsync()
        {
            return await _modelManagementAppService.GetModelsAsync();
        }

        /// <summary>
        /// 获取模型详情
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<ModelDto> GetModelAsync(Guid id)
        {
            return await _modelManagementAppService.GetModelAsync(id);
        }

        /// <summary>
        /// 按名称获取模型
        /// </summary>
        [HttpGet("by-name/{name}")]
        [AllowAnonymous]
        public async Task<ModelDto?> GetModelByNameAsync(string name)
        {
            return await _modelManagementAppService.GetModelByNameAsync(name);
        }

        /// <summary>
        /// 创建模型
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "admin")]
        public async Task<ModelDto> CreateModelAsync([FromBody] CreateModelDto input)
        {
            return await _modelManagementAppService.CreateModelAsync(input);
        }

        /// <summary>
        /// 更新模型
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<ModelDto> UpdateModelAsync(Guid id, [FromBody] UpdateModelDto input)
        {
            return await _modelManagementAppService.UpdateModelAsync(id, input);
        }

        /// <summary>
        /// 删除模型
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteModelAsync(Guid id)
        {
            await _modelManagementAppService.DeleteModelAsync(id);
            return Ok();
        }

        /// <summary>
        /// 启用/禁用模型
        /// </summary>
        [HttpPut("{id}/status")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> SetModelStatusAsync(Guid id, [FromBody] SetModelStatusRequest request)
        {
            await _modelManagementAppService.SetModelStatusAsync(id, request.IsEnabled);
            return Ok();
        }

        /// <summary>
        /// 标记模型为已弃用
        /// </summary>
        [HttpPost("{id}/deprecate")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeprecateModelAsync(Guid id, [FromBody] DeprecateModelRequest request)
        {
            await _modelManagementAppService.DeprecateModelAsync(id, request.ReplacementModel);
            return Ok();
        }

        /// <summary>
        /// 获取模型统计信息
        /// </summary>
        [HttpGet("{id}/statistics")]
        [Authorize(Roles = "admin")]
        public async Task<ModelStatisticsDto> GetModelStatisticsAsync(Guid id)
        {
            return await _modelManagementAppService.GetModelStatisticsAsync(id);
        }

        /// <summary>
        /// 获取模型使用排行
        /// </summary>
        [HttpGet("usage-ranking")]
        [Authorize(Roles = "admin")]
        public async Task<List<ModelUsageDto>> GetModelUsageRankingAsync([FromQuery] int count = 10)
        {
            return await _modelManagementAppService.GetModelUsageRankingAsync(count);
        }

        /// <summary>
        /// 按类型获取模型
        /// </summary>
        [HttpGet("by-type/{type}")]
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsByTypeAsync(ModelType type)
        {
            return await _modelManagementAppService.GetModelsByTypeAsync(type);
        }

        /// <summary>
        /// 按提供商获取模型
        /// </summary>
        [HttpGet("by-provider/{provider}")]
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsByProviderAsync(ModelProvider provider)
        {
            return await _modelManagementAppService.GetModelsByProviderAsync(provider);
        }

        /// <summary>
        /// 获取模型列表 (OpenAI兼容接口)
        /// </summary>
        [HttpGet("/v1/models")]
        [AllowAnonymous]
        public async Task<IActionResult> GetOpenAICompatibleModelsAsync()
        {
            var models = await _modelManagementAppService.GetModelsAsync();
            
            var openaiModels = models.Select(m => new
            {
                id = m.Name,
                @object = "model",
                created = ((DateTimeOffset)m.CreationTime).ToUnixTimeSeconds(),
                owned_by = m.Provider.ToString().ToLower(),
                permission = new[]
                {
                    new
                    {
                        id = $"modelperm-{Guid.NewGuid()}",
                        @object = "model_permission",
                        created = ((DateTimeOffset)m.CreationTime).ToUnixTimeSeconds(),
                        allow_create_engine = false,
                        allow_sampling = true,
                        allow_logprobs = true,
                        allow_search_indices = false,
                        allow_view = true,
                        allow_fine_tuning = false,
                        organization = "*",
                        group = (string?)null,
                        is_blocking = false
                    }
                },
                root = m.Name,
                parent = (string?)null
            }).ToList();

            return Ok(new
            {
                @object = "list",
                data = openaiModels
            });
        }
    }

    // Request DTOs
    public class SetModelStatusRequest
    {
        public bool IsEnabled { get; set; }
    }

    public class DeprecateModelRequest
    {
        public string? ReplacementModel { get; set; }
    }
}
