using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 监控应用服务
    /// </summary>
    [Authorize]
    public class MonitoringAppService : ApplicationService
    {
        private readonly StatisticsService _statisticsService;

        public MonitoringAppService(StatisticsService statisticsService)
        {
            _statisticsService = statisticsService;
        }

        /// <summary>
        /// 获取系统总览统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<SystemOverviewStatistics> GetSystemOverviewAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _statisticsService.GetSystemOverviewAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取请求趋势统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<RequestTrendStatistics>> GetRequestTrendAsync(GetRequestTrendDto input)
        {
            return await _statisticsService.GetRequestTrendAsync(input.StartDate, input.EndDate, input.Interval);
        }

        /// <summary>
        /// 获取模型使用统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<ModelUsageStatistics>> GetModelUsageStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _statisticsService.GetModelUsageStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取渠道性能统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<ChannelPerformanceStatistics>> GetChannelPerformanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _statisticsService.GetChannelPerformanceStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取用户活跃度统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<UserActivityStatistics>> GetUserActivityStatisticsAsync(GetUserActivityDto input)
        {
            return await _statisticsService.GetUserActivityStatisticsAsync(input.StartDate, input.EndDate, input.TopCount);
        }

        /// <summary>
        /// 获取错误分析统计
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<ErrorAnalysisStatistics>> GetErrorAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _statisticsService.GetErrorAnalysisAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取实时监控数据
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<RealTimeMonitoringData> GetRealTimeMonitoringDataAsync()
        {
            return await _statisticsService.GetRealTimeMonitoringDataAsync();
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _statisticsService.GetPerformanceMetricsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取当前用户的使用统计
        /// </summary>
        public async Task<UserPersonalStatistics> GetMyUsageStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var userId = CurrentUser.Id!.Value;
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            // 这里需要实现用户个人统计逻辑
            return new UserPersonalStatistics
            {
                UserId = userId,
                TotalRequests = 0,
                SuccessfulRequests = 0,
                TotalTokens = 0,
                TotalCost = 0,
                StatisticsPeriod = new DateRange(start, end)
            };
        }

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<SystemHealthStatus> GetSystemHealthAsync()
        {
            var realTimeData = await _statisticsService.GetRealTimeMonitoringDataAsync();
            var performanceMetrics = await _statisticsService.GetPerformanceMetricsAsync(DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);

            var healthStatus = new SystemHealthStatus
            {
                OverallStatus = HealthStatus.Healthy,
                LastChecked = DateTime.UtcNow,
                Components = new List<ComponentHealth>()
            };

            // API健康检查
            var apiHealth = new ComponentHealth
            {
                Name = "API",
                Status = realTimeData.CurrentSuccessRate >= 95 ? HealthStatus.Healthy : 
                        realTimeData.CurrentSuccessRate >= 90 ? HealthStatus.Warning : HealthStatus.Critical,
                Details = $"Success Rate: {realTimeData.CurrentSuccessRate:F2}%, RPS: {realTimeData.CurrentRPS:F2}"
            };
            healthStatus.Components.Add(apiHealth);

            // 响应时间健康检查
            var responseTimeHealth = new ComponentHealth
            {
                Name = "Response Time",
                Status = performanceMetrics.AverageResponseTime <= 2000 ? HealthStatus.Healthy :
                        performanceMetrics.AverageResponseTime <= 5000 ? HealthStatus.Warning : HealthStatus.Critical,
                Details = $"Average: {performanceMetrics.AverageResponseTime:F0}ms, P95: {performanceMetrics.P95ResponseTime:F0}ms"
            };
            healthStatus.Components.Add(responseTimeHealth);

            // 系统负载健康检查
            var systemLoadHealth = new ComponentHealth
            {
                Name = "System Load",
                Status = realTimeData.SystemLoad <= 70 ? HealthStatus.Healthy :
                        realTimeData.SystemLoad <= 85 ? HealthStatus.Warning : HealthStatus.Critical,
                Details = $"CPU Usage: {realTimeData.SystemLoad:F1}%"
            };
            healthStatus.Components.Add(systemLoadHealth);

            // 确定整体状态
            if (healthStatus.Components.Any(c => c.Status == HealthStatus.Critical))
                healthStatus.OverallStatus = HealthStatus.Critical;
            else if (healthStatus.Components.Any(c => c.Status == HealthStatus.Warning))
                healthStatus.OverallStatus = HealthStatus.Warning;

            return healthStatus;
        }

        /// <summary>
        /// 获取告警信息
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<SystemAlert>> GetSystemAlertsAsync()
        {
            var alerts = new List<SystemAlert>();
            var realTimeData = await _statisticsService.GetRealTimeMonitoringDataAsync();
            var errorStats = await _statisticsService.GetErrorAnalysisAsync(DateTime.UtcNow.AddHours(-1), DateTime.UtcNow);

            // 成功率告警
            if (realTimeData.CurrentSuccessRate < 95)
            {
                alerts.Add(new SystemAlert
                {
                    Type = AlertType.Performance,
                    Severity = realTimeData.CurrentSuccessRate < 90 ? AlertSeverity.Critical : AlertSeverity.Warning,
                    Title = "Low Success Rate",
                    Message = $"Current success rate is {realTimeData.CurrentSuccessRate:F2}%",
                    Timestamp = DateTime.UtcNow
                });
            }

            // 响应时间告警
            if (realTimeData.CurrentAverageResponseTime > 5000)
            {
                alerts.Add(new SystemAlert
                {
                    Type = AlertType.Performance,
                    Severity = AlertSeverity.Critical,
                    Title = "High Response Time",
                    Message = $"Average response time is {realTimeData.CurrentAverageResponseTime:F0}ms",
                    Timestamp = DateTime.UtcNow
                });
            }

            // 错误率告警
            var totalErrors = errorStats.Sum(e => e.Count);
            if (totalErrors > 100) // 最近1小时超过100个错误
            {
                alerts.Add(new SystemAlert
                {
                    Type = AlertType.Error,
                    Severity = AlertSeverity.Warning,
                    Title = "High Error Count",
                    Message = $"Detected {totalErrors} errors in the last hour",
                    Timestamp = DateTime.UtcNow
                });
            }

            return alerts.OrderByDescending(a => a.Timestamp).ToList();
        }

        /// <summary>
        /// 导出统计报告
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<StatisticsReport> ExportStatisticsReportAsync(ExportReportDto input)
        {
            var overview = await _statisticsService.GetSystemOverviewAsync(input.StartDate, input.EndDate);
            var modelStats = await _statisticsService.GetModelUsageStatisticsAsync(input.StartDate, input.EndDate);
            var channelStats = await _statisticsService.GetChannelPerformanceStatisticsAsync(input.StartDate, input.EndDate);
            var errorStats = await _statisticsService.GetErrorAnalysisAsync(input.StartDate, input.EndDate);
            var performanceMetrics = await _statisticsService.GetPerformanceMetricsAsync(input.StartDate, input.EndDate);

            return new StatisticsReport
            {
                ReportId = Guid.NewGuid(),
                GeneratedAt = DateTime.UtcNow,
                ReportPeriod = new DateRange(input.StartDate, input.EndDate),
                Overview = overview,
                ModelStatistics = modelStats,
                ChannelStatistics = channelStats,
                ErrorStatistics = errorStats,
                PerformanceMetrics = performanceMetrics,
                Summary = GenerateReportSummary(overview, modelStats, channelStats, errorStats)
            };
        }

        private string GenerateReportSummary(
            SystemOverviewStatistics overview,
            List<ModelUsageStatistics> modelStats,
            List<ChannelPerformanceStatistics> channelStats,
            List<ErrorAnalysisStatistics> errorStats)
        {
            var summary = $"Report Summary:\n";
            summary += $"- Total Requests: {overview.TotalRequests:N0}\n";
            summary += $"- Success Rate: {overview.SuccessRate:F2}%\n";
            summary += $"- Total Revenue: ${overview.TotalRevenue:F2}\n";
            summary += $"- Most Used Model: {modelStats.FirstOrDefault()?.ModelName ?? "N/A"}\n";
            summary += $"- Best Performing Channel: {channelStats.OrderBy(c => c.ErrorRate).FirstOrDefault()?.ChannelName ?? "N/A"}\n";
            summary += $"- Top Error: {errorStats.FirstOrDefault()?.ErrorCode ?? "N/A"}";

            return summary;
        }
    }

    // DTOs
    public class GetRequestTrendDto
    {
        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public TrendInterval Interval { get; set; } = TrendInterval.Daily;
    }

    public class GetUserActivityDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [Range(1, 1000)]
        public int TopCount { get; set; } = 50;
    }

    public class ExportReportDto
    {
        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public string? ReportName { get; set; }
    }

    public class UserPersonalStatistics
    {
        public Guid UserId { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public class SystemHealthStatus
    {
        public HealthStatus OverallStatus { get; set; }
        public DateTime LastChecked { get; set; }
        public List<ComponentHealth> Components { get; set; } = new();
    }

    public class ComponentHealth
    {
        public string Name { get; set; } = string.Empty;
        public HealthStatus Status { get; set; }
        public string Details { get; set; } = string.Empty;
    }

    public class SystemAlert
    {
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class StatisticsReport
    {
        public Guid ReportId { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateRange ReportPeriod { get; set; } = new();
        public SystemOverviewStatistics Overview { get; set; } = new();
        public List<ModelUsageStatistics> ModelStatistics { get; set; } = new();
        public List<ChannelPerformanceStatistics> ChannelStatistics { get; set; } = new();
        public List<ErrorAnalysisStatistics> ErrorStatistics { get; set; } = new();
        public PerformanceMetrics PerformanceMetrics { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
    }

    public enum HealthStatus
    {
        Healthy = 1,
        Warning = 2,
        Critical = 3
    }

    public enum AlertType
    {
        Performance = 1,
        Error = 2,
        Security = 3,
        System = 4
    }

    public enum AlertSeverity
    {
        Info = 1,
        Warning = 2,
        Critical = 3
    }
}
