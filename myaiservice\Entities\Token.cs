using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;

namespace myaiservice.Entities
{
    /// <summary>
    /// 令牌实体 - 用户API访问令牌聚合根
    /// </summary>
    public class Token : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 令牌名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 令牌值 (48位安全密钥)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 令牌类型
        /// </summary>
        public TokenType Type { get; set; } = TokenType.ApiAccess;

        /// <summary>
        /// 所属用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否无限配额
        /// </summary>
        public bool IsUnlimited { get; set; } = false;

        /// <summary>
        /// 剩余配额
        /// </summary>
        public decimal RemainingQuota { get; set; } = 0;

        /// <summary>
        /// 已使用配额
        /// </summary>
        public decimal UsedQuota { get; set; } = 0;

        /// <summary>
        /// 请求次数限制 (每分钟)
        /// </summary>
        public int RequestLimitPerMinute { get; set; } = 60;

        /// <summary>
        /// 请求次数限制 (每小时)
        /// </summary>
        public int RequestLimitPerHour { get; set; } = 3600;

        /// <summary>
        /// 请求次数限制 (每天)
        /// </summary>
        public int RequestLimitPerDay { get; set; } = 86400;

        /// <summary>
        /// 允许的模型列表 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? AllowedModels { get; set; }

        /// <summary>
        /// IP白名单 (JSON格式)
        /// </summary>
        [StringLength(1000)]
        public string? IpWhitelist { get; set; }

        /// <summary>
        /// 允许的用户组列表 (JSON格式)
        /// </summary>
        [StringLength(500)]
        public string? AllowedGroups { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 上次使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 上次使用IP
        /// </summary>
        [StringLength(45)]
        public string? LastUsedIp { get; set; }

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests { get; set; } = 0;

        /// <summary>
        /// 成功请求次数
        /// </summary>
        public long SuccessfulRequests { get; set; } = 0;

        /// <summary>
        /// 总Token消耗
        /// </summary>
        public long TotalTokens { get; set; } = 0;

        /// <summary>
        /// 输入Token消耗
        /// </summary>
        public long InputTokens { get; set; } = 0;

        /// <summary>
        /// 输出Token消耗
        /// </summary>
        public long OutputTokens { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public Token()
        {
        }

        public Token(Guid id, string name, string value, Guid userId, TokenType type = TokenType.ApiAccess)
        {
            Id = id;
            Name = name;
            Value = value;
            UserId = userId;
            Type = type;
            IsEnabled = true;
            IsUnlimited = false;
            RemainingQuota = 0;
            UsedQuota = 0;
            RequestLimitPerMinute = 60;
            RequestLimitPerHour = 3600;
            RequestLimitPerDay = 86400;
            TotalRequests = 0;
            SuccessfulRequests = 0;
            TotalTokens = 0;
            InputTokens = 0;
            OutputTokens = 0;
        }

        /// <summary>
        /// 检查是否有足够配额
        /// </summary>
        public bool HasSufficientQuota(decimal requiredQuota)
        {
            if (IsUnlimited)
                return true;

            return RemainingQuota >= requiredQuota;
        }

        /// <summary>
        /// 消费配额
        /// </summary>
        public bool ConsumeQuota(decimal amount)
        {
            if (IsUnlimited)
            {
                UsedQuota += amount;
                return true;
            }

            if (RemainingQuota < amount)
                return false;

            RemainingQuota -= amount;
            UsedQuota += amount;
            return true;
        }

        /// <summary>
        /// 增加配额
        /// </summary>
        public void AddQuota(decimal amount)
        {
            if (!IsUnlimited)
            {
                RemainingQuota += amount;
            }
        }

        /// <summary>
        /// 记录请求使用
        /// </summary>
        public void RecordUsage(string? ipAddress, long inputTokenCount, long outputTokenCount, decimal cost)
        {
            LastUsedTime = DateTime.UtcNow;
            LastUsedIp = ipAddress;
            TotalRequests++;
            SuccessfulRequests++;
            InputTokens += inputTokenCount;
            OutputTokens += outputTokenCount;
            TotalTokens += inputTokenCount + outputTokenCount;
            
            ConsumeQuota(cost);
        }

        /// <summary>
        /// 记录失败请求
        /// </summary>
        public void RecordFailedRequest(string? ipAddress)
        {
            LastUsedTime = DateTime.UtcNow;
            LastUsedIp = ipAddress;
            TotalRequests++;
        }

        /// <summary>
        /// 检查是否过期
        /// </summary>
        public bool IsExpired()
        {
            return ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
        }

        /// <summary>
        /// 检查IP是否在白名单中
        /// </summary>
        public bool IsIpAllowed(string ipAddress)
        {
            if (string.IsNullOrEmpty(IpWhitelist))
                return true;

            // TODO: 实现IP白名单检查逻辑
            return true;
        }

        /// <summary>
        /// 检查模型是否被允许
        /// </summary>
        public bool IsModelAllowed(string modelName)
        {
            if (string.IsNullOrEmpty(AllowedModels))
                return true;

            // TODO: 实现模型权限检查逻辑
            return true;
        }

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double GetSuccessRate()
        {
            if (TotalRequests == 0)
                return 0;

            return (double)SuccessfulRequests / TotalRequests * 100;
        }

        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void ResetStatistics()
        {
            TotalRequests = 0;
            SuccessfulRequests = 0;
            TotalTokens = 0;
            InputTokens = 0;
            OutputTokens = 0;
            UsedQuota = 0;
        }
    }

    /// <summary>
    /// 令牌类型枚举
    /// </summary>
    public enum TokenType
    {
        /// <summary>
        /// 系统管理令牌
        /// </summary>
        SystemManagement = 1,

        /// <summary>
        /// API访问令牌
        /// </summary>
        ApiAccess = 2
    }
}
