﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace myaiservice.Migrations
{
    /// <inheritdoc />
    public partial class AddCoreEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "KeyValue",
                table: "AppKeys",
                newName: "LastUsedTime");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "AppKeys",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "KeyType",
                table: "AppKeys",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AddColumn<double>(
                name: "AverageResponseTime",
                table: "AppKeys",
                type: "REAL",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<int>(
                name: "CurrentKeyIndex",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FailureCount",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "IsEnabled",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "KeyValues",
                table: "AppKeys",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastCheckedTime",
                table: "AppKeys",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxConcurrency",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MaxFailures",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Remarks",
                table: "AppKeys",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "SuccessfulCalls",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TotalCalls",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<int>(
                name: "Weight",
                table: "AppKeys",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "AppChannels",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    BaseUrl = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    ApiKey = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    OrganizationId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ProjectId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Priority = table.Column<int>(type: "INTEGER", nullable: false),
                    Weight = table.Column<int>(type: "INTEGER", nullable: false),
                    SupportedModels = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ModelMapping = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Config = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Group = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MaxConcurrency = table.Column<int>(type: "INTEGER", nullable: false),
                    TimeoutSeconds = table.Column<int>(type: "INTEGER", nullable: false),
                    RetryCount = table.Column<int>(type: "INTEGER", nullable: false),
                    Balance = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    BalanceUpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    AutoCheckBalance = table.Column<bool>(type: "INTEGER", nullable: false),
                    BalanceCheckInterval = table.Column<int>(type: "INTEGER", nullable: false),
                    LastUsedTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastCheckedTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    TotalRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    SuccessfulRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    FailureCount = table.Column<int>(type: "INTEGER", nullable: false),
                    MaxFailures = table.Column<int>(type: "INTEGER", nullable: false),
                    AverageResponseTime = table.Column<double>(type: "REAL", nullable: false),
                    LastError = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    LastErrorTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppChannels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppInvitationCodes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Code = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    CreatedByUserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    UsedByUserId = table.Column<Guid>(type: "TEXT", nullable: true),
                    IsUsed = table.Column<bool>(type: "INTEGER", nullable: false),
                    UsedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    RewardAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppInvitationCodes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppModels",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Provider = table.Column<int>(type: "INTEGER", nullable: false),
                    IsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    InputPrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: false),
                    OutputPrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: false),
                    CachePrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: false),
                    MaxContextLength = table.Column<int>(type: "INTEGER", nullable: false),
                    MaxOutputLength = table.Column<int>(type: "INTEGER", nullable: false),
                    SupportsStreaming = table.Column<bool>(type: "INTEGER", nullable: false),
                    SupportsFunctionCalling = table.Column<bool>(type: "INTEGER", nullable: false),
                    SupportsVision = table.Column<bool>(type: "INTEGER", nullable: false),
                    SupportsAudio = table.Column<bool>(type: "INTEGER", nullable: false),
                    SupportsImageGeneration = table.Column<bool>(type: "INTEGER", nullable: false),
                    SupportedFileTypes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Version = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ReleaseDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeprecated = table.Column<bool>(type: "INTEGER", nullable: false),
                    DeprecatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ReplacementModel = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SortOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    TotalRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    SuccessfulRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    TotalTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    InputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    OutputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    TotalCost = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    AverageResponseTime = table.Column<double>(type: "REAL", nullable: false),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppModels", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppQuotas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    TotalQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    UsedQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    IsUnlimited = table.Column<bool>(type: "INTEGER", nullable: false),
                    Source = table.Column<int>(type: "INTEGER", nullable: false),
                    SourceDescription = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastUsedTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppQuotas", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppQuotaTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    QuotaId = table.Column<Guid>(type: "TEXT", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    BalanceBefore = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    BalanceAfter = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    TokenId = table.Column<Guid>(type: "TEXT", nullable: true),
                    RequestLogId = table.Column<Guid>(type: "TEXT", nullable: true),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppQuotaTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppRequestLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: true),
                    TokenId = table.Column<Guid>(type: "TEXT", nullable: true),
                    ChannelId = table.Column<Guid>(type: "TEXT", nullable: true),
                    ModelId = table.Column<Guid>(type: "TEXT", nullable: true),
                    RequestPath = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    RequestMethod = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    RequestIp = table.Column<string>(type: "TEXT", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RequestHeaders = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    RequestBody = table.Column<string>(type: "TEXT", nullable: true),
                    ResponseStatusCode = table.Column<int>(type: "INTEGER", nullable: false),
                    ResponseHeaders = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ResponseBody = table.Column<string>(type: "TEXT", nullable: true),
                    RequestStartTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    RequestEndTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ResponseTime = table.Column<double>(type: "REAL", nullable: false),
                    IsSuccess = table.Column<bool>(type: "INTEGER", nullable: false),
                    ErrorMessage = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    ErrorCode = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ModelName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    InputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    OutputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    CacheTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    Cost = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: false),
                    IsStreaming = table.Column<bool>(type: "INTEGER", nullable: false),
                    StreamChunks = table.Column<int>(type: "INTEGER", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    ContentType = table.Column<int>(type: "INTEGER", nullable: false),
                    FileCount = table.Column<int>(type: "INTEGER", nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    RetryCount = table.Column<int>(type: "INTEGER", nullable: false),
                    ChannelName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ChannelType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppRequestLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    IsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsUnlimited = table.Column<bool>(type: "INTEGER", nullable: false),
                    RemainingQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    UsedQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    RequestLimitPerMinute = table.Column<int>(type: "INTEGER", nullable: false),
                    RequestLimitPerHour = table.Column<int>(type: "INTEGER", nullable: false),
                    RequestLimitPerDay = table.Column<int>(type: "INTEGER", nullable: false),
                    AllowedModels = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    IpWhitelist = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    AllowedGroups = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastUsedTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastUsedIp = table.Column<string>(type: "TEXT", maxLength: 45, nullable: true),
                    TotalRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    SuccessfulRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    TotalTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    InputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    OutputTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppTokens", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppUserProfiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    UserId = table.Column<Guid>(type: "TEXT", nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    UserGroup = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    InvitationCode = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true),
                    InvitedBy = table.Column<Guid>(type: "TEXT", nullable: true),
                    InvitedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    TotalQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    UsedQuota = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    IsUnlimitedQuota = table.Column<bool>(type: "INTEGER", nullable: false),
                    RequestLimitPerMinute = table.Column<int>(type: "INTEGER", nullable: false),
                    RequestLimitPerHour = table.Column<int>(type: "INTEGER", nullable: false),
                    RequestLimitPerDay = table.Column<int>(type: "INTEGER", nullable: false),
                    IpWhitelist = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    AllowedModels = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    LastLoginTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastLoginIp = table.Column<string>(type: "TEXT", maxLength: 45, nullable: true),
                    LoginCount = table.Column<int>(type: "INTEGER", nullable: false),
                    TotalRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    SuccessfulRequests = table.Column<long>(type: "INTEGER", nullable: false),
                    TotalTokens = table.Column<long>(type: "INTEGER", nullable: false),
                    TotalCost = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    InvitedUserCount = table.Column<int>(type: "INTEGER", nullable: false),
                    InvitationRewards = table.Column<decimal>(type: "TEXT", precision: 18, scale: 4, nullable: false),
                    ThirdPartyLogins = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Preferences = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Remarks = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ExtraProperties = table.Column<string>(type: "TEXT", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatorId = table.Column<Guid>(type: "TEXT", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppUserProfiles", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppKeys_KeyType",
                table: "AppKeys",
                column: "KeyType");

            migrationBuilder.CreateIndex(
                name: "IX_AppKeys_Name",
                table: "AppKeys",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_AppChannels_Name",
                table: "AppChannels",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_AppChannels_Status",
                table: "AppChannels",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AppChannels_Type",
                table: "AppChannels",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppInvitationCodes_Code",
                table: "AppInvitationCodes",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppInvitationCodes_CreatedByUserId",
                table: "AppInvitationCodes",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppInvitationCodes_IsUsed",
                table: "AppInvitationCodes",
                column: "IsUsed");

            migrationBuilder.CreateIndex(
                name: "IX_AppModels_Name",
                table: "AppModels",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppModels_Provider",
                table: "AppModels",
                column: "Provider");

            migrationBuilder.CreateIndex(
                name: "IX_AppModels_Type",
                table: "AppModels",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotas_Source",
                table: "AppQuotas",
                column: "Source");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotas_Type",
                table: "AppQuotas",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotas_UserId",
                table: "AppQuotas",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotaTransactions_QuotaId",
                table: "AppQuotaTransactions",
                column: "QuotaId");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotaTransactions_Type",
                table: "AppQuotaTransactions",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppQuotaTransactions_UserId",
                table: "AppQuotaTransactions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_ChannelId",
                table: "AppRequestLogs",
                column: "ChannelId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_ModelId",
                table: "AppRequestLogs",
                column: "ModelId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_RequestStartTime",
                table: "AppRequestLogs",
                column: "RequestStartTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_TokenId",
                table: "AppRequestLogs",
                column: "TokenId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_Type",
                table: "AppRequestLogs",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppRequestLogs_UserId",
                table: "AppRequestLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppTokens_Type",
                table: "AppTokens",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_AppTokens_UserId",
                table: "AppTokens",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppTokens_Value",
                table: "AppTokens",
                column: "Value",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppUserProfiles_InvitationCode",
                table: "AppUserProfiles",
                column: "InvitationCode");

            migrationBuilder.CreateIndex(
                name: "IX_AppUserProfiles_Status",
                table: "AppUserProfiles",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AppUserProfiles_UserId",
                table: "AppUserProfiles",
                column: "UserId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppChannels");

            migrationBuilder.DropTable(
                name: "AppInvitationCodes");

            migrationBuilder.DropTable(
                name: "AppModels");

            migrationBuilder.DropTable(
                name: "AppQuotas");

            migrationBuilder.DropTable(
                name: "AppQuotaTransactions");

            migrationBuilder.DropTable(
                name: "AppRequestLogs");

            migrationBuilder.DropTable(
                name: "AppTokens");

            migrationBuilder.DropTable(
                name: "AppUserProfiles");

            migrationBuilder.DropIndex(
                name: "IX_AppKeys_KeyType",
                table: "AppKeys");

            migrationBuilder.DropIndex(
                name: "IX_AppKeys_Name",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "AverageResponseTime",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "CurrentKeyIndex",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "FailureCount",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "IsEnabled",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "KeyValues",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "LastCheckedTime",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "MaxConcurrency",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "MaxFailures",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "Remarks",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "SuccessfulCalls",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "TotalCalls",
                table: "AppKeys");

            migrationBuilder.DropColumn(
                name: "Weight",
                table: "AppKeys");

            migrationBuilder.RenameColumn(
                name: "LastUsedTime",
                table: "AppKeys",
                newName: "KeyValue");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "AppKeys",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "KeyType",
                table: "AppKeys",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);
        }
    }
}
