using Microsoft.Extensions.Localization;
using myaiservice.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace myaiservice;

[Dependency(ReplaceServices = true)]
public class myaiserviceBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<myaiserviceResource> _localizer;

    public myaiserviceBrandingProvider(IStringLocalizer<myaiserviceResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}