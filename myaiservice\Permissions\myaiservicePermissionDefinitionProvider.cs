using myaiservice.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace myaiservice.Permissions;

public class myaiservicePermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(myaiservicePermissions.GroupName);


        //Define your own permissions here. Example:
        //myGroup.AddPermission(myaiservicePermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<myaiserviceResource>(name);
    }
}
