using myaiservice.Entities;
using myaiservice.Entities.ValueObjects;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 计费服务 - 处理费用计算和扣费逻辑
    /// </summary>
    public class BillingService : DomainService
    {
        private readonly IRepository<Model, Guid> _modelRepository;
        private readonly IRepository<Quota, Guid> _quotaRepository;
        private readonly IRepository<QuotaTransaction, Guid> _quotaTransactionRepository;
        private readonly QuotaDomainService _quotaDomainService;

        public BillingService(
            IRepository<Model, Guid> modelRepository,
            IRepository<Quota, Guid> quotaRepository,
            IRepository<QuotaTransaction, Guid> quotaTransactionRepository,
            QuotaDomainService quotaDomainService)
        {
            _modelRepository = modelRepository;
            _quotaRepository = quotaRepository;
            _quotaTransactionRepository = quotaTransactionRepository;
            _quotaDomainService = quotaDomainService;
        }

        /// <summary>
        /// 计算请求费用
        /// </summary>
        public async Task<BillingResult> CalculateRequestCostAsync(string modelName, TokenUsage tokenUsage, RequestType requestType)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                throw new InvalidOperationException($"Model '{modelName}' not found");

            var cost = CalculateBaseCost(model, tokenUsage, requestType);
            var adjustedCost = ApplyPricingRules(cost, model, tokenUsage, requestType);

            return new BillingResult
            {
                ModelId = model.Id,
                ModelName = modelName,
                TokenUsage = tokenUsage,
                BaseCost = cost,
                AdjustedCost = adjustedCost,
                Currency = "USD",
                PricingBreakdown = CreatePricingBreakdown(model, tokenUsage, cost, adjustedCost)
            };
        }

        /// <summary>
        /// 预估请求费用
        /// </summary>
        public async Task<decimal> EstimateRequestCostAsync(string modelName, int estimatedInputTokens, int estimatedOutputTokens)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                return 0;

            var tokenUsage = new TokenUsage(estimatedInputTokens, estimatedOutputTokens);
            return CalculateBaseCost(model, tokenUsage, RequestType.ChatCompletion);
        }

        /// <summary>
        /// 处理请求计费
        /// </summary>
        public async Task<BillingProcessResult> ProcessRequestBillingAsync(Guid userId, Guid? tokenId, string modelName, TokenUsage tokenUsage, RequestType requestType, Guid? requestLogId = null)
        {
            var billingResult = await CalculateRequestCostAsync(modelName, tokenUsage, requestType);
            
            // 检查用户配额
            var (hasSufficientQuota, availableQuota) = await _quotaDomainService.CheckUserQuotaAsync(userId, QuotaType.General, billingResult.AdjustedCost);
            
            if (!hasSufficientQuota)
            {
                return new BillingProcessResult
                {
                    Success = false,
                    ErrorMessage = "Insufficient quota",
                    BillingResult = billingResult,
                    AvailableQuota = availableQuota,
                    RequiredQuota = billingResult.AdjustedCost
                };
            }

            // 扣费
            var consumeSuccess = await _quotaDomainService.ConsumeUserQuotaAsync(
                userId, 
                QuotaType.General, 
                billingResult.AdjustedCost, 
                $"API request - {modelName}", 
                tokenId, 
                requestLogId);

            if (!consumeSuccess)
            {
                return new BillingProcessResult
                {
                    Success = false,
                    ErrorMessage = "Failed to consume quota",
                    BillingResult = billingResult
                };
            }

            return new BillingProcessResult
            {
                Success = true,
                BillingResult = billingResult,
                ConsumedQuota = billingResult.AdjustedCost
            };
        }

        /// <summary>
        /// 退款处理
        /// </summary>
        public async Task<bool> ProcessRefundAsync(Guid userId, decimal amount, string reason, Guid? relatedTransactionId = null)
        {
            try
            {
                await _quotaDomainService.RefundUserQuotaAsync(userId, QuotaType.General, amount, $"Refund: {reason}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取用户计费统计
        /// </summary>
        public async Task<UserBillingStatistics> GetUserBillingStatisticsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var quotas = await _quotaRepository.GetListAsync(q => q.UserId == userId);
            var transactions = await _quotaTransactionRepository.GetListAsync(t => 
                t.UserId == userId && 
                (!startDate.HasValue || t.CreationTime >= startDate.Value) &&
                (!endDate.HasValue || t.CreationTime <= endDate.Value));

            var totalSpent = transactions.Where(t => t.Type == QuotaTransactionType.Consumption).Sum(t => t.Amount);
            var totalRecharged = transactions.Where(t => t.Type == QuotaTransactionType.Recharge).Sum(t => t.Amount);
            var totalRefunded = transactions.Where(t => t.Type == QuotaTransactionType.Refund).Sum(t => t.Amount);

            return new UserBillingStatistics
            {
                UserId = userId,
                TotalQuota = quotas.Sum(q => q.TotalQuota),
                UsedQuota = quotas.Sum(q => q.UsedQuota),
                RemainingQuota = quotas.Sum(q => q.RemainingQuota),
                TotalSpent = totalSpent,
                TotalRecharged = totalRecharged,
                TotalRefunded = totalRefunded,
                TransactionCount = transactions.Count,
                AverageTransactionAmount = transactions.Count > 0 ? transactions.Average(t => t.Amount) : 0,
                LastTransactionTime = transactions.OrderByDescending(t => t.CreationTime).FirstOrDefault()?.CreationTime,
                StatisticsPeriod = new DateRange(startDate ?? DateTime.MinValue, endDate ?? DateTime.MaxValue)
            };
        }

        /// <summary>
        /// 获取模型计费统计
        /// </summary>
        public async Task<List<ModelBillingStatistics>> GetModelBillingStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var models = await _modelRepository.GetListAsync();
            var statistics = new List<ModelBillingStatistics>();

            foreach (var model in models)
            {
                var modelStats = new ModelBillingStatistics
                {
                    ModelId = model.Id,
                    ModelName = model.Name,
                    TotalRequests = model.TotalRequests,
                    TotalTokens = model.TotalTokens,
                    TotalCost = model.TotalCost,
                    AverageCostPerRequest = model.GetAverageCostPerRequest(),
                    AverageCostPerToken = model.TotalTokens > 0 ? model.TotalCost / model.TotalTokens : 0
                };

                statistics.Add(modelStats);
            }

            return statistics.OrderByDescending(s => s.TotalCost).ToList();
        }

        /// <summary>
        /// 计算基础费用
        /// </summary>
        private decimal CalculateBaseCost(Model model, TokenUsage tokenUsage, RequestType requestType)
        {
            return requestType switch
            {
                RequestType.ChatCompletion or RequestType.TextCompletion => 
                    model.CalculateCost(tokenUsage.InputTokens, tokenUsage.OutputTokens, tokenUsage.CacheTokens),
                RequestType.Embedding => 
                    model.CalculateCost(tokenUsage.InputTokens, 0, 0),
                RequestType.ImageGeneration => 
                    model.InputPrice, // 图像生成通常按张计费
                RequestType.SpeechToText => 
                    model.CalculateCost(tokenUsage.InputTokens, 0, 0),
                RequestType.TextToSpeech => 
                    model.CalculateCost(tokenUsage.InputTokens, 0, 0),
                _ => 0
            };
        }

        /// <summary>
        /// 应用定价规则
        /// </summary>
        private decimal ApplyPricingRules(decimal baseCost, Model model, TokenUsage tokenUsage, RequestType requestType)
        {
            var adjustedCost = baseCost;

            // 批量折扣
            if (tokenUsage.TotalTokens > 10000)
            {
                adjustedCost *= 0.95m; // 5% 折扣
            }
            else if (tokenUsage.TotalTokens > 50000)
            {
                adjustedCost *= 0.9m; // 10% 折扣
            }

            // 最小费用
            var minimumCost = 0.0001m;
            if (adjustedCost < minimumCost)
            {
                adjustedCost = minimumCost;
            }

            return Math.Round(adjustedCost, 6);
        }

        /// <summary>
        /// 创建定价明细
        /// </summary>
        private PricingBreakdown CreatePricingBreakdown(Model model, TokenUsage tokenUsage, decimal baseCost, decimal adjustedCost)
        {
            var breakdown = new PricingBreakdown();

            if (tokenUsage.InputTokens > 0)
            {
                breakdown.Items.Add(new PricingItem
                {
                    Description = "Input tokens",
                    Quantity = tokenUsage.InputTokens,
                    UnitPrice = model.InputPrice,
                    Amount = (decimal)tokenUsage.InputTokens / 1000 * model.InputPrice
                });
            }

            if (tokenUsage.OutputTokens > 0)
            {
                breakdown.Items.Add(new PricingItem
                {
                    Description = "Output tokens",
                    Quantity = tokenUsage.OutputTokens,
                    UnitPrice = model.OutputPrice,
                    Amount = (decimal)tokenUsage.OutputTokens / 1000 * model.OutputPrice
                });
            }

            if (tokenUsage.CacheTokens > 0)
            {
                breakdown.Items.Add(new PricingItem
                {
                    Description = "Cache tokens",
                    Quantity = tokenUsage.CacheTokens,
                    UnitPrice = model.CachePrice,
                    Amount = (decimal)tokenUsage.CacheTokens / 1000 * model.CachePrice
                });
            }

            breakdown.Subtotal = baseCost;
            breakdown.Discount = baseCost - adjustedCost;
            breakdown.Total = adjustedCost;

            return breakdown;
        }
    }

    // 计费相关的数据传输对象
    public class BillingResult
    {
        public Guid ModelId { get; set; }
        public string ModelName { get; set; } = string.Empty;
        public TokenUsage TokenUsage { get; set; } = TokenUsage.Empty;
        public decimal BaseCost { get; set; }
        public decimal AdjustedCost { get; set; }
        public string Currency { get; set; } = "USD";
        public PricingBreakdown PricingBreakdown { get; set; } = new();
    }

    public class BillingProcessResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public BillingResult BillingResult { get; set; } = new();
        public decimal AvailableQuota { get; set; }
        public decimal RequiredQuota { get; set; }
        public decimal ConsumedQuota { get; set; }
    }

    public class UserBillingStatistics
    {
        public Guid UserId { get; set; }
        public decimal TotalQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public decimal RemainingQuota { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal TotalRecharged { get; set; }
        public decimal TotalRefunded { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionAmount { get; set; }
        public DateTime? LastTransactionTime { get; set; }
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public class ModelBillingStatistics
    {
        public Guid ModelId { get; set; }
        public string ModelName { get; set; } = string.Empty;
        public long TotalRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public decimal AverageCostPerRequest { get; set; }
        public decimal AverageCostPerToken { get; set; }
    }

    public class PricingBreakdown
    {
        public List<PricingItem> Items { get; set; } = new();
        public decimal Subtotal { get; set; }
        public decimal Discount { get; set; }
        public decimal Total { get; set; }
    }

    public class PricingItem
    {
        public string Description { get; set; } = string.Empty;
        public long Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Amount { get; set; }
    }

    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public DateRange() { }

        public DateRange(DateTime startDate, DateTime endDate)
        {
            StartDate = startDate;
            EndDate = endDate;
        }
    }
}
