using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 日志管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LogManagementController : AbpControllerBase
    {
        private readonly LogManagementAppService _logManagementAppService;

        public LogManagementController(LogManagementAppService logManagementAppService)
        {
            _logManagementAppService = logManagementAppService;
        }

        /// <summary>
        /// 获取请求日志列表
        /// </summary>
        [HttpPost("request-logs")]
        [Authorize(Roles = "admin")]
        public async Task<List<RequestLogDto>> GetRequestLogsAsync([FromBody] GetRequestLogsDto input)
        {
            return await _logManagementAppService.GetRequestLogsAsync(input);
        }

        /// <summary>
        /// 获取请求日志详情
        /// </summary>
        [HttpGet("request-logs/{id}")]
        [Authorize(Roles = "admin")]
        public async Task<RequestLogDetailDto> GetRequestLogDetailAsync(Guid id)
        {
            return await _logManagementAppService.GetRequestLogDetailAsync(id);
        }

        /// <summary>
        /// 获取当前用户的请求日志
        /// </summary>
        [HttpPost("my-request-logs")]
        public async Task<List<RequestLogDto>> GetMyRequestLogsAsync([FromBody] GetMyRequestLogsDto input)
        {
            return await _logManagementAppService.GetMyRequestLogsAsync(input);
        }

        /// <summary>
        /// 搜索日志
        /// </summary>
        [HttpPost("search")]
        [Authorize(Roles = "admin")]
        public async Task<List<RequestLogDto>> SearchLogsAsync([FromBody] SearchLogsDto input)
        {
            return await _logManagementAppService.SearchLogsAsync(input);
        }

        /// <summary>
        /// 导出请求日志
        /// </summary>
        [HttpPost("export")]
        [Authorize(Roles = "admin")]
        public async Task<LogExportResult> ExportRequestLogsAsync([FromBody] ExportRequestLogsDto input)
        {
            return await _logManagementAppService.ExportRequestLogsAsync(input);
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin")]
        public async Task<LogStatistics> GetLogStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _logManagementAppService.GetLogStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 删除过期日志
        /// </summary>
        [HttpDelete("expired")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteExpiredLogsAsync([FromQuery] int retentionDays = 90)
        {
            var deletedCount = await _logManagementAppService.DeleteExpiredLogsAsync(retentionDays);
            return Ok(new { success = true, message = $"Deleted {deletedCount} expired log entries", deletedCount });
        }

        /// <summary>
        /// 获取日志文件列表
        /// </summary>
        [HttpGet("files")]
        [Authorize(Roles = "admin")]
        public async Task<List<LogFileInfo>> GetLogFilesAsync()
        {
            // 简化实现，实际应该扫描日志目录
            return await Task.FromResult(new List<LogFileInfo>
            {
                new LogFileInfo
                {
                    FileName = "application.log",
                    FilePath = "/logs/application.log",
                    Size = 1024 * 1024 * 50, // 50MB
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    ModifiedAt = DateTime.UtcNow.AddMinutes(-5),
                    LogLevel = "Information",
                    LineCount = 125000
                },
                new LogFileInfo
                {
                    FileName = "error.log",
                    FilePath = "/logs/error.log",
                    Size = 1024 * 1024 * 5, // 5MB
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    ModifiedAt = DateTime.UtcNow.AddHours(-2),
                    LogLevel = "Error",
                    LineCount = 2500
                },
                new LogFileInfo
                {
                    FileName = "audit.log",
                    FilePath = "/logs/audit.log",
                    Size = 1024 * 1024 * 20, // 20MB
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    ModifiedAt = DateTime.UtcNow.AddMinutes(-10),
                    LogLevel = "Information",
                    LineCount = 45000
                }
            });
        }

        /// <summary>
        /// 下载日志文件
        /// </summary>
        [HttpGet("files/{fileName}/download")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DownloadLogFileAsync(string fileName)
        {
            // 简化实现，实际应该读取真实的日志文件
            var content = $"Sample log content for {fileName}\nGenerated at: {DateTime.UtcNow}";
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);
            
            return await Task.FromResult(File(bytes, "text/plain", fileName));
        }

        /// <summary>
        /// 获取日志文件内容预览
        /// </summary>
        [HttpGet("files/{fileName}/preview")]
        [Authorize(Roles = "admin")]
        public async Task<LogFilePreview> GetLogFilePreviewAsync(string fileName, [FromQuery] int lines = 100)
        {
            // 简化实现
            var sampleLines = new List<string>();
            for (int i = 1; i <= lines; i++)
            {
                sampleLines.Add($"[{DateTime.UtcNow.AddMinutes(-i):yyyy-MM-dd HH:mm:ss}] INFO: Sample log entry {i}");
            }

            return await Task.FromResult(new LogFilePreview
            {
                FileName = fileName,
                TotalLines = 125000,
                PreviewLines = sampleLines,
                LastModified = DateTime.UtcNow.AddMinutes(-5),
                FileSize = 1024 * 1024 * 50
            });
        }

        /// <summary>
        /// 清理日志文件
        /// </summary>
        [HttpPost("files/cleanup")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> CleanupLogFilesAsync([FromBody] LogCleanupRequest request)
        {
            // 简化实现
            var deletedFiles = new List<string> { "old_application.log", "old_error.log" };
            var freedSpace = 1024 * 1024 * 100; // 100MB

            return await Task.FromResult(Ok(new
            {
                success = true,
                message = "Log cleanup completed",
                deletedFiles,
                freedSpace,
                deletedCount = deletedFiles.Count
            }));
        }

        /// <summary>
        /// 归档日志文件
        /// </summary>
        [HttpPost("files/{fileName}/archive")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> ArchiveLogFileAsync(string fileName)
        {
            // 简化实现
            var archiveFileName = $"{fileName}.{DateTime.UtcNow:yyyyMMdd}.gz";
            
            return await Task.FromResult(Ok(new
            {
                success = true,
                message = "Log file archived successfully",
                originalFile = fileName,
                archiveFile = archiveFileName,
                compressionRatio = 0.75
            }));
        }

        /// <summary>
        /// 获取实时日志流
        /// </summary>
        [HttpGet("stream")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> GetLogStreamAsync([FromQuery] string level = "Information")
        {
            // 简化实现，实际应该实现WebSocket或Server-Sent Events
            var recentLogs = new List<LogEntry>
            {
                new LogEntry
                {
                    Timestamp = DateTime.UtcNow,
                    Level = "Information",
                    Message = "API request processed successfully",
                    Source = "ApiProxyController",
                    RequestId = Guid.NewGuid().ToString()
                },
                new LogEntry
                {
                    Timestamp = DateTime.UtcNow.AddSeconds(-5),
                    Level = "Warning",
                    Message = "High response time detected",
                    Source = "PerformanceMonitor",
                    RequestId = Guid.NewGuid().ToString()
                }
            };

            return await Task.FromResult(Ok(recentLogs));
        }
    }

    // 日志管理相关的数据传输对象
    public class LogFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ModifiedAt { get; set; }
        public string LogLevel { get; set; } = string.Empty;
        public int LineCount { get; set; }
    }

    public class LogFilePreview
    {
        public string FileName { get; set; } = string.Empty;
        public int TotalLines { get; set; }
        public List<string> PreviewLines { get; set; } = new();
        public DateTime LastModified { get; set; }
        public long FileSize { get; set; }
    }

    public class LogCleanupRequest
    {
        public int RetentionDays { get; set; } = 30;
        public List<string> LogLevels { get; set; } = new();
        public bool CompressOldLogs { get; set; } = true;
        public long MaxTotalSize { get; set; } = 1024 * 1024 * 1024; // 1GB
    }

    public class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string? RequestId { get; set; }
        public string? Exception { get; set; }
        public Dictionary<string, object>? Properties { get; set; }
    }
}
