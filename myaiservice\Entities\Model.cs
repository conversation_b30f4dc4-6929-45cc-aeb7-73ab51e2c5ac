using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// AI模型实体
    /// </summary>
    public class Model : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 模型名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模型显示名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 模型类型
        /// </summary>
        public ModelType Type { get; set; }

        /// <summary>
        /// 模型提供商
        /// </summary>
        public ModelProvider Provider { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 输入价格 (每1K tokens)
        /// </summary>
        public decimal InputPrice { get; set; } = 0;

        /// <summary>
        /// 输出价格 (每1K tokens)
        /// </summary>
        public decimal OutputPrice { get; set; } = 0;

        /// <summary>
        /// 缓存价格 (每1K tokens)
        /// </summary>
        public decimal CachePrice { get; set; } = 0;

        /// <summary>
        /// 最大上下文长度
        /// </summary>
        public int MaxContextLength { get; set; } = 4096;

        /// <summary>
        /// 最大输出长度
        /// </summary>
        public int MaxOutputLength { get; set; } = 2048;

        /// <summary>
        /// 是否支持流式输出
        /// </summary>
        public bool SupportsStreaming { get; set; } = true;

        /// <summary>
        /// 是否支持函数调用
        /// </summary>
        public bool SupportsFunctionCalling { get; set; } = false;

        /// <summary>
        /// 是否支持视觉输入
        /// </summary>
        public bool SupportsVision { get; set; } = false;

        /// <summary>
        /// 是否支持音频输入
        /// </summary>
        public bool SupportsAudio { get; set; } = false;

        /// <summary>
        /// 是否支持图像生成
        /// </summary>
        public bool SupportsImageGeneration { get; set; } = false;

        /// <summary>
        /// 支持的文件类型 (JSON格式)
        /// </summary>
        [StringLength(500)]
        public string? SupportedFileTypes { get; set; }

        /// <summary>
        /// 模型描述
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// 模型版本
        /// </summary>
        [StringLength(50)]
        public string? Version { get; set; }

        /// <summary>
        /// 发布日期
        /// </summary>
        public DateTime? ReleaseDate { get; set; }

        /// <summary>
        /// 是否已弃用
        /// </summary>
        public bool IsDeprecated { get; set; } = false;

        /// <summary>
        /// 弃用日期
        /// </summary>
        public DateTime? DeprecatedDate { get; set; }

        /// <summary>
        /// 替代模型
        /// </summary>
        [StringLength(100)]
        public string? ReplacementModel { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests { get; set; } = 0;

        /// <summary>
        /// 成功请求次数
        /// </summary>
        public long SuccessfulRequests { get; set; } = 0;

        /// <summary>
        /// 总Token消耗
        /// </summary>
        public long TotalTokens { get; set; } = 0;

        /// <summary>
        /// 输入Token消耗
        /// </summary>
        public long InputTokens { get; set; } = 0;

        /// <summary>
        /// 输出Token消耗
        /// </summary>
        public long OutputTokens { get; set; } = 0;

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; } = 0;

        /// <summary>
        /// 平均响应时间 (毫秒)
        /// </summary>
        public double AverageResponseTime { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public Model()
        {
        }

        public Model(Guid id, string name, string displayName, ModelType type, ModelProvider provider)
        {
            Id = id;
            Name = name;
            DisplayName = displayName;
            Type = type;
            Provider = provider;
            IsEnabled = true;
            InputPrice = 0;
            OutputPrice = 0;
            CachePrice = 0;
            MaxContextLength = 4096;
            MaxOutputLength = 2048;
            SupportsStreaming = true;
            SupportsFunctionCalling = false;
            SupportsVision = false;
            SupportsAudio = false;
            SupportsImageGeneration = false;
            IsDeprecated = false;
            SortOrder = 0;
            TotalRequests = 0;
            SuccessfulRequests = 0;
            TotalTokens = 0;
            InputTokens = 0;
            OutputTokens = 0;
            TotalCost = 0;
            AverageResponseTime = 0;
        }

        /// <summary>
        /// 计算请求成本
        /// </summary>
        public decimal CalculateCost(long inputTokenCount, long outputTokenCount, long cacheTokenCount = 0)
        {
            var inputCost = (decimal)inputTokenCount / 1000 * InputPrice;
            var outputCost = (decimal)outputTokenCount / 1000 * OutputPrice;
            var cacheCost = (decimal)cacheTokenCount / 1000 * CachePrice;
            
            return inputCost + outputCost + cacheCost;
        }

        /// <summary>
        /// 记录使用统计
        /// </summary>
        public void RecordUsage(long inputTokenCount, long outputTokenCount, decimal cost, double responseTime)
        {
            TotalRequests++;
            SuccessfulRequests++;
            InputTokens += inputTokenCount;
            OutputTokens += outputTokenCount;
            TotalTokens += inputTokenCount + outputTokenCount;
            TotalCost += cost;
            
            // 更新平均响应时间
            if (SuccessfulRequests == 1)
            {
                AverageResponseTime = responseTime;
            }
            else
            {
                AverageResponseTime = (AverageResponseTime * (SuccessfulRequests - 1) + responseTime) / SuccessfulRequests;
            }
        }

        /// <summary>
        /// 记录失败请求
        /// </summary>
        public void RecordFailedRequest()
        {
            TotalRequests++;
        }

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double GetSuccessRate()
        {
            if (TotalRequests == 0)
                return 0;

            return (double)SuccessfulRequests / TotalRequests * 100;
        }

        /// <summary>
        /// 获取平均每次请求成本
        /// </summary>
        public decimal GetAverageCostPerRequest()
        {
            if (SuccessfulRequests == 0)
                return 0;

            return TotalCost / SuccessfulRequests;
        }

        /// <summary>
        /// 标记为已弃用
        /// </summary>
        public void MarkAsDeprecated(string? replacementModel = null)
        {
            IsDeprecated = true;
            DeprecatedDate = DateTime.UtcNow;
            ReplacementModel = replacementModel;
        }

        /// <summary>
        /// 检查是否支持指定功能
        /// </summary>
        public bool SupportsFeature(ModelFeature feature)
        {
            return feature switch
            {
                ModelFeature.Streaming => SupportsStreaming,
                ModelFeature.FunctionCalling => SupportsFunctionCalling,
                ModelFeature.Vision => SupportsVision,
                ModelFeature.Audio => SupportsAudio,
                ModelFeature.ImageGeneration => SupportsImageGeneration,
                _ => false
            };
        }
    }

    /// <summary>
    /// 模型类型枚举
    /// </summary>
    public enum ModelType
    {
        /// <summary>
        /// 文本生成
        /// </summary>
        TextGeneration = 1,

        /// <summary>
        /// 聊天对话
        /// </summary>
        Chat = 2,

        /// <summary>
        /// 文本嵌入
        /// </summary>
        Embedding = 3,

        /// <summary>
        /// 图像生成
        /// </summary>
        ImageGeneration = 4,

        /// <summary>
        /// 语音识别
        /// </summary>
        SpeechToText = 5,

        /// <summary>
        /// 语音合成
        /// </summary>
        TextToSpeech = 6,

        /// <summary>
        /// 重排序
        /// </summary>
        Rerank = 7,

        /// <summary>
        /// 多模态
        /// </summary>
        Multimodal = 8
    }

    /// <summary>
    /// 模型提供商枚举
    /// </summary>
    public enum ModelProvider
    {
        OpenAI = 1,
        Anthropic = 2,
        Google = 3,
        Microsoft = 4,
        Baidu = 5,
        Xunfei = 6,
        Zhipu = 7,
        Moonshot = 8,
        DeepSeek = 9,
        Alibaba = 10,
        Tencent = 11,
        ByteDance = 12,
        Other = 99
    }

    /// <summary>
    /// 模型功能枚举
    /// </summary>
    public enum ModelFeature
    {
        Streaming = 1,
        FunctionCalling = 2,
        Vision = 3,
        Audio = 4,
        ImageGeneration = 5
    }
}
