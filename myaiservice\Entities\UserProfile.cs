using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 用户扩展资料实体
    /// </summary>
    public class UserProfile : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 用户ID (关联到ABP Identity User)
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户状态
        /// </summary>
        public UserStatus Status { get; set; } = UserStatus.Active;

        /// <summary>
        /// 用户组
        /// </summary>
        [StringLength(100)]
        public string? UserGroup { get; set; }

        /// <summary>
        /// 邀请码
        /// </summary>
        [StringLength(20)]
        public string? InvitationCode { get; set; }

        /// <summary>
        /// 邀请人ID
        /// </summary>
        public Guid? InvitedBy { get; set; }

        /// <summary>
        /// 邀请时间
        /// </summary>
        public DateTime? InvitedAt { get; set; }

        /// <summary>
        /// 总配额
        /// </summary>
        public decimal TotalQuota { get; set; } = 0;

        /// <summary>
        /// 已使用配额
        /// </summary>
        public decimal UsedQuota { get; set; } = 0;

        /// <summary>
        /// 剩余配额
        /// </summary>
        public decimal RemainingQuota => TotalQuota - UsedQuota;

        /// <summary>
        /// 是否无限配额
        /// </summary>
        public bool IsUnlimitedQuota { get; set; } = false;

        /// <summary>
        /// 请求次数限制 (每分钟)
        /// </summary>
        public int RequestLimitPerMinute { get; set; } = 60;

        /// <summary>
        /// 请求次数限制 (每小时)
        /// </summary>
        public int RequestLimitPerHour { get; set; } = 3600;

        /// <summary>
        /// 请求次数限制 (每天)
        /// </summary>
        public int RequestLimitPerDay { get; set; } = 86400;

        /// <summary>
        /// IP白名单 (JSON格式)
        /// </summary>
        [StringLength(1000)]
        public string? IpWhitelist { get; set; }

        /// <summary>
        /// 允许的模型列表 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? AllowedModels { get; set; }

        /// <summary>
        /// 上次登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 上次登录IP
        /// </summary>
        [StringLength(45)]
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 登录次数
        /// </summary>
        public int LoginCount { get; set; } = 0;

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests { get; set; } = 0;

        /// <summary>
        /// 成功请求次数
        /// </summary>
        public long SuccessfulRequests { get; set; } = 0;

        /// <summary>
        /// 总Token消耗
        /// </summary>
        public long TotalTokens { get; set; } = 0;

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; } = 0;

        /// <summary>
        /// 邀请的用户数量
        /// </summary>
        public int InvitedUserCount { get; set; } = 0;

        /// <summary>
        /// 邀请奖励总额
        /// </summary>
        public decimal InvitationRewards { get; set; } = 0;

        /// <summary>
        /// 第三方登录信息 (JSON格式)
        /// </summary>
        [StringLength(1000)]
        public string? ThirdPartyLogins { get; set; }

        /// <summary>
        /// 用户偏好设置 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? Preferences { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public UserProfile()
        {
        }

        public UserProfile(Guid id, Guid userId)
        {
            Id = id;
            UserId = userId;
            Status = UserStatus.Active;
            TotalQuota = 0;
            UsedQuota = 0;
            IsUnlimitedQuota = false;
            RequestLimitPerMinute = 60;
            RequestLimitPerHour = 3600;
            RequestLimitPerDay = 86400;
            LoginCount = 0;
            TotalRequests = 0;
            SuccessfulRequests = 0;
            TotalTokens = 0;
            TotalCost = 0;
            InvitedUserCount = 0;
            InvitationRewards = 0;
        }

        /// <summary>
        /// 检查是否有足够配额
        /// </summary>
        public bool HasSufficientQuota(decimal requiredQuota)
        {
            if (IsUnlimitedQuota)
                return true;

            return RemainingQuota >= requiredQuota;
        }

        /// <summary>
        /// 消费配额
        /// </summary>
        public bool ConsumeQuota(decimal amount)
        {
            if (IsUnlimitedQuota)
            {
                UsedQuota += amount;
                return true;
            }

            if (RemainingQuota < amount)
                return false;

            UsedQuota += amount;
            return true;
        }

        /// <summary>
        /// 增加配额
        /// </summary>
        public void AddQuota(decimal amount)
        {
            if (!IsUnlimitedQuota)
            {
                TotalQuota += amount;
            }
        }

        /// <summary>
        /// 记录登录
        /// </summary>
        public void RecordLogin(string? ipAddress)
        {
            LastLoginTime = DateTime.UtcNow;
            LastLoginIp = ipAddress;
            LoginCount++;
        }

        /// <summary>
        /// 记录请求使用
        /// </summary>
        public void RecordUsage(long tokenCount, decimal cost, bool isSuccess)
        {
            TotalRequests++;
            if (isSuccess)
            {
                SuccessfulRequests++;
            }
            TotalTokens += tokenCount;
            TotalCost += cost;
            
            ConsumeQuota(cost);
        }

        /// <summary>
        /// 记录邀请用户
        /// </summary>
        public void RecordInvitation(decimal rewardAmount)
        {
            InvitedUserCount++;
            InvitationRewards += rewardAmount;
            AddQuota(rewardAmount);
        }

        /// <summary>
        /// 激活用户
        /// </summary>
        public void Activate()
        {
            Status = UserStatus.Active;
        }

        /// <summary>
        /// 禁用用户
        /// </summary>
        public void Disable()
        {
            Status = UserStatus.Disabled;
        }

        /// <summary>
        /// 暂停用户
        /// </summary>
        public void Suspend()
        {
            Status = UserStatus.Suspended;
        }

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double GetSuccessRate()
        {
            if (TotalRequests == 0)
                return 0;

            return (double)SuccessfulRequests / TotalRequests * 100;
        }

        /// <summary>
        /// 获取配额使用率
        /// </summary>
        public double GetQuotaUsageRate()
        {
            if (IsUnlimitedQuota || TotalQuota == 0)
                return 0;

            return (double)(UsedQuota / TotalQuota) * 100;
        }

        /// <summary>
        /// 检查IP是否在白名单中
        /// </summary>
        public bool IsIpAllowed(string ipAddress)
        {
            if (string.IsNullOrEmpty(IpWhitelist))
                return true;

            // TODO: 实现IP白名单检查逻辑
            return true;
        }

        /// <summary>
        /// 检查模型是否被允许
        /// </summary>
        public bool IsModelAllowed(string modelName)
        {
            if (string.IsNullOrEmpty(AllowedModels))
                return true;

            // TODO: 实现模型权限检查逻辑
            return true;
        }
    }

    /// <summary>
    /// 用户状态枚举
    /// </summary>
    public enum UserStatus
    {
        /// <summary>
        /// 活跃
        /// </summary>
        Active = 1,

        /// <summary>
        /// 禁用
        /// </summary>
        Disabled = 2,

        /// <summary>
        /// 暂停
        /// </summary>
        Suspended = 3,

        /// <summary>
        /// 待激活
        /// </summary>
        PendingActivation = 4
    }

    /// <summary>
    /// 邀请码实体
    /// </summary>
    public class InvitationCode : AuditedEntity<Guid>
    {
        /// <summary>
        /// 邀请码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        public Guid CreatedByUserId { get; set; }

        /// <summary>
        /// 使用者用户ID
        /// </summary>
        public Guid? UsedByUserId { get; set; }

        /// <summary>
        /// 是否已使用
        /// </summary>
        public bool IsUsed { get; set; } = false;

        /// <summary>
        /// 使用时间
        /// </summary>
        public DateTime? UsedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 奖励金额
        /// </summary>
        public decimal RewardAmount { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(200)]
        public string? Remarks { get; set; }

        public InvitationCode()
        {
        }

        public InvitationCode(Guid id, string code, Guid createdByUserId, decimal rewardAmount, DateTime? expiresAt = null)
        {
            Id = id;
            Code = code;
            CreatedByUserId = createdByUserId;
            RewardAmount = rewardAmount;
            ExpiresAt = expiresAt;
            IsUsed = false;
        }

        /// <summary>
        /// 使用邀请码
        /// </summary>
        public bool Use(Guid userId)
        {
            if (IsUsed || (ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow))
                return false;

            UsedByUserId = userId;
            IsUsed = true;
            UsedAt = DateTime.UtcNow;
            return true;
        }

        /// <summary>
        /// 检查是否过期
        /// </summary>
        public bool IsExpired()
        {
            return ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
        }
    }
}
