﻿using Microsoft.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using myaiservice.Entities;

namespace myaiservice.Data;

public class myaiserviceDbContext : AbpDbContext<myaiserviceDbContext>
{
    
    public const string DbTablePrefix = "App";
    public const string DbSchema = null;

    public myaiserviceDbContext(DbContextOptions<myaiserviceDbContext> options)
        : base(options)
    {
    }


    public DbSet<ApiKey> ApiKeys { get; set; }
    public DbSet<Key> Keys { get; set; }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigurePermissionManagement();
        builder.ConfigureBlobStoring();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        
        /* Configure your own entities here */

        builder.Entity<ApiKey>(b =>
        {
            b.ToTable(DbTablePrefix + "ApiKeys", DbSchema);
            b.ConfigureByConvention(); //auto configure for the base class props
            b.Property(x => x.Value).IsRequired().HasMaxLength(100);
            b.HasIndex(x => x.Value).IsUnique();
        });

        builder.Entity<Key>(b => 
        {
            b.ToTable(DbTablePrefix + "Keys", DbSchema);
            b.ConfigureByConvention(); //auto configure for the base class props
          
        });
    }
}

