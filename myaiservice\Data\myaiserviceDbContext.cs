﻿using Microsoft.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using myaiservice.Entities;

namespace myaiservice.Data;

public class myaiserviceDbContext : AbpDbContext<myaiserviceDbContext>
{
    
    public const string DbTablePrefix = "App";
    public const string DbSchema = null;

    public myaiserviceDbContext(DbContextOptions<myaiserviceDbContext> options)
        : base(options)
    {
    }


    // 原有实体
    public DbSet<ApiKey> ApiKeys { get; set; }
    public DbSet<Key> Keys { get; set; }

    // 新增核心业务实体
    public DbSet<Token> Tokens { get; set; }
    public DbSet<Channel> Channels { get; set; }
    public DbSet<Model> Models { get; set; }
    public DbSet<Quota> Quotas { get; set; }
    public DbSet<QuotaTransaction> QuotaTransactions { get; set; }
    public DbSet<RequestLog> RequestLogs { get; set; }
    public DbSet<UserProfile> UserProfiles { get; set; }
    public DbSet<InvitationCode> InvitationCodes { get; set; }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigurePermissionManagement();
        builder.ConfigureBlobStoring();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        
        /* Configure your own entities here */

        builder.Entity<ApiKey>(b =>
        {
            b.ToTable(DbTablePrefix + "ApiKeys", DbSchema);
            b.ConfigureByConvention(); //auto configure for the base class props
            b.Property(x => x.Value).IsRequired().HasMaxLength(100);
            b.HasIndex(x => x.Value).IsUnique();
        });

        builder.Entity<Key>(b =>
        {
            b.ToTable(DbTablePrefix + "Keys", DbSchema);
            b.ConfigureByConvention(); //auto configure for the base class props
            b.Property(x => x.Name).IsRequired().HasMaxLength(100);
            b.Property(x => x.KeyType).IsRequired().HasMaxLength(50);
            b.Property(x => x.KeyValues).HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions)null) ?? new List<string>());
            b.HasIndex(x => x.Name);
            b.HasIndex(x => x.KeyType);
        });

        // Token实体配置
        builder.Entity<Token>(b =>
        {
            b.ToTable(DbTablePrefix + "Tokens", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Name).IsRequired().HasMaxLength(100);
            b.Property(x => x.Value).IsRequired().HasMaxLength(100);
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.RemainingQuota).HasPrecision(18, 4);
            b.Property(x => x.UsedQuota).HasPrecision(18, 4);
            b.HasIndex(x => x.Value).IsUnique();
            b.HasIndex(x => x.UserId);
            b.HasIndex(x => x.Type);
        });

        // Channel实体配置
        builder.Entity<Channel>(b =>
        {
            b.ToTable(DbTablePrefix + "Channels", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Name).IsRequired().HasMaxLength(100);
            b.Property(x => x.BaseUrl).IsRequired().HasMaxLength(500);
            b.Property(x => x.ApiKey).IsRequired().HasMaxLength(500);
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.Status).HasConversion<int>();
            b.Property(x => x.Balance).HasPrecision(18, 4);
            b.HasIndex(x => x.Name);
            b.HasIndex(x => x.Type);
            b.HasIndex(x => x.Status);
        });

        // Model实体配置
        builder.Entity<Model>(b =>
        {
            b.ToTable(DbTablePrefix + "Models", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Name).IsRequired().HasMaxLength(100);
            b.Property(x => x.DisplayName).IsRequired().HasMaxLength(100);
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.Provider).HasConversion<int>();
            b.Property(x => x.InputPrice).HasPrecision(18, 6);
            b.Property(x => x.OutputPrice).HasPrecision(18, 6);
            b.Property(x => x.CachePrice).HasPrecision(18, 6);
            b.Property(x => x.TotalCost).HasPrecision(18, 4);
            b.HasIndex(x => x.Name).IsUnique();
            b.HasIndex(x => x.Type);
            b.HasIndex(x => x.Provider);
        });

        // Quota实体配置
        builder.Entity<Quota>(b =>
        {
            b.ToTable(DbTablePrefix + "Quotas", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.Source).HasConversion<int>();
            b.Property(x => x.TotalQuota).HasPrecision(18, 4);
            b.Property(x => x.UsedQuota).HasPrecision(18, 4);
            b.Ignore(x => x.RemainingQuota);
            b.Ignore(x => x.IsExpired);
            b.HasIndex(x => x.UserId);
            b.HasIndex(x => x.Type);
            b.HasIndex(x => x.Source);
        });

        // QuotaTransaction实体配置
        builder.Entity<QuotaTransaction>(b =>
        {
            b.ToTable(DbTablePrefix + "QuotaTransactions", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.Amount).HasPrecision(18, 4);
            b.Property(x => x.BalanceBefore).HasPrecision(18, 4);
            b.Property(x => x.BalanceAfter).HasPrecision(18, 4);
            b.HasIndex(x => x.UserId);
            b.HasIndex(x => x.QuotaId);
            b.HasIndex(x => x.Type);
        });

        // RequestLog实体配置
        builder.Entity<RequestLog>(b =>
        {
            b.ToTable(DbTablePrefix + "RequestLogs", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.RequestPath).IsRequired().HasMaxLength(200);
            b.Property(x => x.RequestMethod).IsRequired().HasMaxLength(10);
            b.Property(x => x.Type).HasConversion<int>();
            b.Property(x => x.ContentType).HasConversion<int>();
            b.Property(x => x.Cost).HasPrecision(18, 6);
            b.Ignore(x => x.TotalTokens);
            b.HasIndex(x => x.UserId);
            b.HasIndex(x => x.TokenId);
            b.HasIndex(x => x.ChannelId);
            b.HasIndex(x => x.ModelId);
            b.HasIndex(x => x.RequestStartTime);
            b.HasIndex(x => x.Type);
        });

        // UserProfile实体配置
        builder.Entity<UserProfile>(b =>
        {
            b.ToTable(DbTablePrefix + "UserProfiles", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Status).HasConversion<int>();
            b.Property(x => x.TotalQuota).HasPrecision(18, 4);
            b.Property(x => x.UsedQuota).HasPrecision(18, 4);
            b.Property(x => x.TotalCost).HasPrecision(18, 4);
            b.Property(x => x.InvitationRewards).HasPrecision(18, 4);
            b.Ignore(x => x.RemainingQuota);
            b.HasIndex(x => x.UserId).IsUnique();
            b.HasIndex(x => x.InvitationCode);
            b.HasIndex(x => x.Status);
        });

        // InvitationCode实体配置
        builder.Entity<InvitationCode>(b =>
        {
            b.ToTable(DbTablePrefix + "InvitationCodes", DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.Code).IsRequired().HasMaxLength(20);
            b.Property(x => x.RewardAmount).HasPrecision(18, 4);
            b.HasIndex(x => x.Code).IsUnique();
            b.HasIndex(x => x.CreatedByUserId);
            b.HasIndex(x => x.IsUsed);
        });
    }
}

