using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Services.DomainServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 管理后台仪表板控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "admin")]
    public class DashboardController : AbpControllerBase
    {
        private readonly MonitoringAppService _monitoringAppService;
        private readonly UserManagementAppService _userManagementAppService;
        private readonly TokenManagementAppService _tokenManagementAppService;
        private readonly ChannelManagementAppService _channelManagementAppService;
        private readonly ModelManagementAppService _modelManagementAppService;
        private readonly QuotaManagementAppService _quotaManagementAppService;

        public DashboardController(
            MonitoringAppService monitoringAppService,
            UserManagementAppService userManagementAppService,
            TokenManagementAppService tokenManagementAppService,
            ChannelManagementAppService channelManagementAppService,
            ModelManagementAppService modelManagementAppService,
            QuotaManagementAppService quotaManagementAppService)
        {
            _monitoringAppService = monitoringAppService;
            _userManagementAppService = userManagementAppService;
            _tokenManagementAppService = tokenManagementAppService;
            _channelManagementAppService = channelManagementAppService;
            _modelManagementAppService = modelManagementAppService;
            _quotaManagementAppService = quotaManagementAppService;
        }

        /// <summary>
        /// 获取仪表板总览数据
        /// </summary>
        [HttpGet("overview")]
        public async Task<AdminDashboardOverview> GetDashboardOverviewAsync()
        {
            var systemOverview = await _monitoringAppService.GetSystemOverviewAsync(DateTime.UtcNow.Date.AddDays(-7), DateTime.UtcNow);
            var realTimeData = await _monitoringAppService.GetRealTimeMonitoringDataAsync();
            var healthStatus = await _monitoringAppService.GetSystemHealthAsync();
            var alerts = await _monitoringAppService.GetSystemAlertsAsync();

            // 获取用户统计
            var userStats = await _userManagementAppService.GetUserStatisticsAsync();
            
            // 获取Token统计
            var tokenStats = await _tokenManagementAppService.GetTokenStatisticsAsync();
            
            // 获取渠道统计
            var channelStats = await _channelManagementAppService.GetChannelStatisticsAsync();
            
            // 获取模型统计
            var modelStats = await _modelManagementAppService.GetModelStatisticsAsync();

            return new AdminDashboardOverview
            {
                SystemOverview = systemOverview,
                RealTimeData = realTimeData,
                HealthStatus = healthStatus,
                RecentAlerts = alerts.Take(5).ToList(),
                UserStatistics = userStats,
                TokenStatistics = tokenStats,
                ChannelStatistics = channelStats,
                ModelStatistics = modelStats,
                LastUpdated = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 获取快速操作数据
        /// </summary>
        [HttpGet("quick-actions")]
        public async Task<QuickActionsData> GetQuickActionsAsync()
        {
            var pendingUsers = await _userManagementAppService.GetPendingUsersAsync();
            var expiredTokens = await _tokenManagementAppService.GetExpiredTokensAsync();
            var inactiveChannels = await _channelManagementAppService.GetInactiveChannelsAsync();
            var lowQuotaUsers = await _quotaManagementAppService.GetLowQuotaUsersAsync();

            return new QuickActionsData
            {
                PendingUserCount = pendingUsers.Count,
                ExpiredTokenCount = expiredTokens.Count,
                InactiveChannelCount = inactiveChannels.Count,
                LowQuotaUserCount = lowQuotaUsers.Count,
                PendingUsers = pendingUsers.Take(10).ToList(),
                ExpiredTokens = expiredTokens.Take(10).ToList(),
                InactiveChannels = inactiveChannels.Take(10).ToList(),
                LowQuotaUsers = lowQuotaUsers.Take(10).ToList()
            };
        }

        /// <summary>
        /// 获取系统配置信息
        /// </summary>
        [HttpGet("system-config")]
        public async Task<SystemConfigInfo> GetSystemConfigAsync()
        {
            return new SystemConfigInfo
            {
                Version = "1.0.0",
                Environment = "Production",
                DatabaseType = "SQLite",
                CacheProvider = "Memory",
                LogLevel = "Information",
                MaxRequestsPerMinute = 1000,
                MaxTokensPerRequest = 4096,
                DefaultQuotaAmount = 10.0m,
                TokenExpirationDays = 365,
                LogRetentionDays = 90,
                BackupEnabled = true,
                MonitoringEnabled = true,
                LastBackupTime = DateTime.UtcNow.AddHours(-6),
                SystemStartTime = DateTime.UtcNow.AddDays(-15)
            };
        }

        /// <summary>
        /// 获取资源使用情况
        /// </summary>
        [HttpGet("resource-usage")]
        public async Task<ResourceUsageInfo> GetResourceUsageAsync()
        {
            // 简化实现，实际应该从系统监控获取真实数据
            return await Task.FromResult(new ResourceUsageInfo
            {
                CpuUsage = 25.5,
                MemoryUsage = 68.2,
                DiskUsage = 45.8,
                NetworkIn = 1024 * 1024 * 50, // 50MB
                NetworkOut = 1024 * 1024 * 120, // 120MB
                DatabaseSize = 1024 * 1024 * 1024 * 2, // 2GB
                LogFileSize = 1024 * 1024 * 500, // 500MB
                CacheSize = 1024 * 1024 * 256, // 256MB
                ActiveConnections = 45,
                QueuedRequests = 12,
                LastUpdated = DateTime.UtcNow
            });
        }

        /// <summary>
        /// 获取最近活动
        /// </summary>
        [HttpGet("recent-activities")]
        public async Task<List<AdminActivity>> GetRecentActivitiesAsync([FromQuery] int count = 20)
        {
            // 简化实现，实际应该从审计日志获取
            return await Task.FromResult(new List<AdminActivity>
            {
                new AdminActivity
                {
                    Id = Guid.NewGuid(),
                    Type = ActivityType.UserCreated,
                    Description = "Created new user: <EMAIL>",
                    UserId = Guid.NewGuid(),
                    UserName = "admin",
                    Timestamp = DateTime.UtcNow.AddMinutes(-5),
                    IpAddress = "*************",
                    Details = "User created with default quota"
                },
                new AdminActivity
                {
                    Id = Guid.NewGuid(),
                    Type = ActivityType.TokenGenerated,
                    Description = "Generated new API token",
                    UserId = Guid.NewGuid(),
                    UserName = "admin",
                    Timestamp = DateTime.UtcNow.AddMinutes(-15),
                    IpAddress = "*************",
                    Details = "Token expires in 365 days"
                },
                new AdminActivity
                {
                    Id = Guid.NewGuid(),
                    Type = ActivityType.ChannelUpdated,
                    Description = "Updated OpenAI channel configuration",
                    UserId = Guid.NewGuid(),
                    UserName = "admin",
                    Timestamp = DateTime.UtcNow.AddMinutes(-30),
                    IpAddress = "*************",
                    Details = "Updated API key and timeout settings"
                }
            }.Take(count).ToList());
        }

        /// <summary>
        /// 获取系统通知
        /// </summary>
        [HttpGet("notifications")]
        public async Task<List<SystemNotification>> GetSystemNotificationsAsync()
        {
            var alerts = await _monitoringAppService.GetSystemAlertsAsync();
            var notifications = new List<SystemNotification>();

            foreach (var alert in alerts)
            {
                notifications.Add(new SystemNotification
                {
                    Id = Guid.NewGuid(),
                    Type = alert.Type switch
                    {
                        AlertType.Performance => NotificationType.Warning,
                        AlertType.Error => NotificationType.Error,
                        AlertType.Security => NotificationType.Critical,
                        _ => NotificationType.Info
                    },
                    Title = alert.Title,
                    Message = alert.Message,
                    IsRead = false,
                    CreatedAt = alert.Timestamp,
                    Priority = alert.Severity switch
                    {
                        AlertSeverity.Critical => NotificationPriority.High,
                        AlertSeverity.Warning => NotificationPriority.Medium,
                        _ => NotificationPriority.Low
                    }
                });
            }

            return notifications;
        }

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        [HttpPost("notifications/{id}/mark-read")]
        public async Task<IActionResult> MarkNotificationAsReadAsync(Guid id)
        {
            // 简化实现
            return await Task.FromResult(Ok(new { success = true, message = "Notification marked as read" }));
        }

        /// <summary>
        /// 执行系统维护操作
        /// </summary>
        [HttpPost("maintenance/{action}")]
        public async Task<IActionResult> ExecuteMaintenanceActionAsync(string action)
        {
            return action.ToLower() switch
            {
                "cleanup-logs" => await CleanupLogsAsync(),
                "refresh-cache" => await RefreshCacheAsync(),
                "backup-database" => await BackupDatabaseAsync(),
                "restart-services" => await RestartServicesAsync(),
                _ => BadRequest(new { error = "Unknown maintenance action" })
            };
        }

        private async Task<IActionResult> CleanupLogsAsync()
        {
            // 实现日志清理逻辑
            return await Task.FromResult(Ok(new { success = true, message = "Log cleanup completed", deletedCount = 1250 }));
        }

        private async Task<IActionResult> RefreshCacheAsync()
        {
            // 实现缓存刷新逻辑
            return await Task.FromResult(Ok(new { success = true, message = "Cache refreshed successfully" }));
        }

        private async Task<IActionResult> BackupDatabaseAsync()
        {
            // 实现数据库备份逻辑
            return await Task.FromResult(Ok(new { success = true, message = "Database backup started", backupId = Guid.NewGuid() }));
        }

        private async Task<IActionResult> RestartServicesAsync()
        {
            // 实现服务重启逻辑
            return await Task.FromResult(Ok(new { success = true, message = "Services restart scheduled" }));
        }
    }

    // 管理后台相关的数据传输对象
    public class AdminDashboardOverview
    {
        public SystemOverviewStatistics SystemOverview { get; set; } = new();
        public RealTimeMonitoringData RealTimeData { get; set; } = new();
        public SystemHealthStatus HealthStatus { get; set; } = new();
        public List<SystemAlert> RecentAlerts { get; set; } = new();
        public UserStatisticsDto UserStatistics { get; set; } = new();
        public TokenStatisticsDto TokenStatistics { get; set; } = new();
        public ChannelStatisticsDto ChannelStatistics { get; set; } = new();
        public ModelStatisticsDto ModelStatistics { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    public class QuickActionsData
    {
        public int PendingUserCount { get; set; }
        public int ExpiredTokenCount { get; set; }
        public int InactiveChannelCount { get; set; }
        public int LowQuotaUserCount { get; set; }
        public List<UserDto> PendingUsers { get; set; } = new();
        public List<TokenDto> ExpiredTokens { get; set; } = new();
        public List<ChannelDto> InactiveChannels { get; set; } = new();
        public List<UserDto> LowQuotaUsers { get; set; } = new();
    }

    public class SystemConfigInfo
    {
        public string Version { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public string DatabaseType { get; set; } = string.Empty;
        public string CacheProvider { get; set; } = string.Empty;
        public string LogLevel { get; set; } = string.Empty;
        public int MaxRequestsPerMinute { get; set; }
        public int MaxTokensPerRequest { get; set; }
        public decimal DefaultQuotaAmount { get; set; }
        public int TokenExpirationDays { get; set; }
        public int LogRetentionDays { get; set; }
        public bool BackupEnabled { get; set; }
        public bool MonitoringEnabled { get; set; }
        public DateTime? LastBackupTime { get; set; }
        public DateTime SystemStartTime { get; set; }
    }

    public class ResourceUsageInfo
    {
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public long NetworkIn { get; set; }
        public long NetworkOut { get; set; }
        public long DatabaseSize { get; set; }
        public long LogFileSize { get; set; }
        public long CacheSize { get; set; }
        public int ActiveConnections { get; set; }
        public int QueuedRequests { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class AdminActivity
    {
        public Guid Id { get; set; }
        public ActivityType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string? Details { get; set; }
    }

    public class SystemNotification
    {
        public Guid Id { get; set; }
        public NotificationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public NotificationPriority Priority { get; set; }
    }

    public enum ActivityType
    {
        UserCreated = 1,
        UserUpdated = 2,
        UserDeleted = 3,
        TokenGenerated = 4,
        TokenRevoked = 5,
        ChannelCreated = 6,
        ChannelUpdated = 7,
        ChannelDeleted = 8,
        ModelUpdated = 9,
        QuotaRecharged = 10,
        SystemMaintenance = 11
    }

    public enum NotificationType
    {
        Info = 1,
        Warning = 2,
        Error = 3,
        Critical = 4
    }

    public enum NotificationPriority
    {
        Low = 1,
        Medium = 2,
        High = 3
    }
}
