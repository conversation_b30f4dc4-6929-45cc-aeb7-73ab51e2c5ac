using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 配额实体 - 用户配额管理聚合根
    /// </summary>
    public class Quota : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 配额类型
        /// </summary>
        public QuotaType Type { get; set; }

        /// <summary>
        /// 总配额
        /// </summary>
        public decimal TotalQuota { get; set; } = 0;

        /// <summary>
        /// 已使用配额
        /// </summary>
        public decimal UsedQuota { get; set; } = 0;

        /// <summary>
        /// 剩余配额
        /// </summary>
        public decimal RemainingQuota => TotalQuota - UsedQuota;

        /// <summary>
        /// 是否无限配额
        /// </summary>
        public bool IsUnlimited { get; set; } = false;

        /// <summary>
        /// 配额来源
        /// </summary>
        public QuotaSource Source { get; set; }

        /// <summary>
        /// 来源描述
        /// </summary>
        [StringLength(200)]
        public string? SourceDescription { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;

        /// <summary>
        /// 上次使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public Quota()
        {
        }

        public Quota(Guid id, Guid userId, QuotaType type, decimal totalQuota, QuotaSource source, DateTime? expiresAt = null)
        {
            Id = id;
            UserId = userId;
            Type = type;
            TotalQuota = totalQuota;
            UsedQuota = 0;
            IsUnlimited = false;
            Source = source;
            ExpiresAt = expiresAt;
        }

        /// <summary>
        /// 检查是否有足够配额
        /// </summary>
        public bool HasSufficientQuota(decimal requiredQuota)
        {
            if (IsUnlimited)
                return true;

            if (IsExpired)
                return false;

            return RemainingQuota >= requiredQuota;
        }

        /// <summary>
        /// 消费配额
        /// </summary>
        public bool ConsumeQuota(decimal amount)
        {
            if (IsExpired)
                return false;

            if (IsUnlimited)
            {
                UsedQuota += amount;
                LastUsedTime = DateTime.UtcNow;
                return true;
            }

            if (RemainingQuota < amount)
                return false;

            UsedQuota += amount;
            LastUsedTime = DateTime.UtcNow;
            return true;
        }

        /// <summary>
        /// 增加配额
        /// </summary>
        public void AddQuota(decimal amount)
        {
            if (!IsUnlimited)
            {
                TotalQuota += amount;
            }
        }

        /// <summary>
        /// 重置配额
        /// </summary>
        public void ResetQuota(decimal newTotalQuota)
        {
            TotalQuota = newTotalQuota;
            UsedQuota = 0;
        }

        /// <summary>
        /// 设置为无限配额
        /// </summary>
        public void SetUnlimited()
        {
            IsUnlimited = true;
            ExpiresAt = null;
        }

        /// <summary>
        /// 设置过期时间
        /// </summary>
        public void SetExpiration(DateTime expiresAt)
        {
            ExpiresAt = expiresAt;
        }

        /// <summary>
        /// 获取使用率
        /// </summary>
        public double GetUsageRate()
        {
            if (IsUnlimited || TotalQuota == 0)
                return 0;

            return (double)(UsedQuota / TotalQuota) * 100;
        }
    }

    /// <summary>
    /// 配额类型枚举
    /// </summary>
    public enum QuotaType
    {
        /// <summary>
        /// 通用配额 (按金额)
        /// </summary>
        General = 1,

        /// <summary>
        /// Token配额
        /// </summary>
        Token = 2,

        /// <summary>
        /// 请求次数配额
        /// </summary>
        Request = 3,

        /// <summary>
        /// 图像生成配额
        /// </summary>
        ImageGeneration = 4,

        /// <summary>
        /// 音频处理配额
        /// </summary>
        AudioProcessing = 5
    }

    /// <summary>
    /// 配额来源枚举
    /// </summary>
    public enum QuotaSource
    {
        /// <summary>
        /// 系统赠送
        /// </summary>
        SystemGrant = 1,

        /// <summary>
        /// 用户充值
        /// </summary>
        UserRecharge = 2,

        /// <summary>
        /// 管理员分配
        /// </summary>
        AdminAllocation = 3,

        /// <summary>
        /// 邀请奖励
        /// </summary>
        InvitationReward = 4,

        /// <summary>
        /// 活动奖励
        /// </summary>
        ActivityReward = 5,

        /// <summary>
        /// 充值码兑换
        /// </summary>
        RedeemCode = 6
    }

    /// <summary>
    /// 配额交易记录实体
    /// </summary>
    public class QuotaTransaction : AuditedEntity<Guid>
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 配额ID
        /// </summary>
        public Guid QuotaId { get; set; }

        /// <summary>
        /// 交易类型
        /// </summary>
        public QuotaTransactionType Type { get; set; }

        /// <summary>
        /// 交易金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 交易前余额
        /// </summary>
        public decimal BalanceBefore { get; set; }

        /// <summary>
        /// 交易后余额
        /// </summary>
        public decimal BalanceAfter { get; set; }

        /// <summary>
        /// 交易描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 关联的令牌ID
        /// </summary>
        public Guid? TokenId { get; set; }

        /// <summary>
        /// 关联的请求日志ID
        /// </summary>
        public Guid? RequestLogId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public QuotaTransaction()
        {
        }

        public QuotaTransaction(Guid id, Guid userId, Guid quotaId, QuotaTransactionType type, 
            decimal amount, decimal balanceBefore, decimal balanceAfter, string? description = null)
        {
            Id = id;
            UserId = userId;
            QuotaId = quotaId;
            Type = type;
            Amount = amount;
            BalanceBefore = balanceBefore;
            BalanceAfter = balanceAfter;
            Description = description;
        }
    }

    /// <summary>
    /// 配额交易类型枚举
    /// </summary>
    public enum QuotaTransactionType
    {
        /// <summary>
        /// 充值
        /// </summary>
        Recharge = 1,

        /// <summary>
        /// 消费
        /// </summary>
        Consumption = 2,

        /// <summary>
        /// 退款
        /// </summary>
        Refund = 3,

        /// <summary>
        /// 管理员调整
        /// </summary>
        AdminAdjustment = 4,

        /// <summary>
        /// 奖励
        /// </summary>
        Reward = 5,

        /// <summary>
        /// 过期扣除
        /// </summary>
        Expiration = 6
    }
}
