﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.4" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="9.1.0" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Web" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Identity.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.OpenIddict.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="0.9.25" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
      <PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  
  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Localization\myaiservice\*.json" />
    <EmbeddedResource Include="Localization\myaiservice\*.json" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

  <ItemGroup>
    <None Update="sql.db3">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Providers\" />
  </ItemGroup>

</Project>
