using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    // Model DTOs
    public class CreateModelDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; } = string.Empty;

        [Required]
        public ModelType Type { get; set; }

        [Required]
        public ModelProvider Provider { get; set; }

        public decimal InputPrice { get; set; } = 0;

        public decimal OutputPrice { get; set; } = 0;

        public decimal CachePrice { get; set; } = 0;

        public int MaxContextLength { get; set; } = 4096;

        public int MaxOutputLength { get; set; } = 2048;

        public bool SupportsStreaming { get; set; } = true;

        public bool SupportsFunctionCalling { get; set; } = false;

        public bool SupportsVision { get; set; } = false;

        public bool SupportsAudio { get; set; } = false;

        public bool SupportsImageGeneration { get; set; } = false;

        public string? SupportedFileTypes { get; set; }

        public string? Description { get; set; }

        public string? Version { get; set; }

        public DateTime? ReleaseDate { get; set; }

        public int SortOrder { get; set; } = 0;

        public string? Remarks { get; set; }
    }

    public class ModelDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public ModelType Type { get; set; }
        public ModelProvider Provider { get; set; }
        public bool IsEnabled { get; set; }
        public decimal InputPrice { get; set; }
        public decimal OutputPrice { get; set; }
        public decimal CachePrice { get; set; }
        public int MaxContextLength { get; set; }
        public int MaxOutputLength { get; set; }
        public bool SupportsStreaming { get; set; }
        public bool SupportsFunctionCalling { get; set; }
        public bool SupportsVision { get; set; }
        public bool SupportsAudio { get; set; }
        public bool SupportsImageGeneration { get; set; }
        public string? SupportedFileTypes { get; set; }
        public string? Description { get; set; }
        public string? Version { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public bool IsDeprecated { get; set; }
        public DateTime? DeprecatedDate { get; set; }
        public string? ReplacementModel { get; set; }
        public int SortOrder { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long TotalTokens { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public decimal TotalCost { get; set; }
        public double AverageResponseTime { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    // Quota DTOs
    public class QuotaDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public QuotaType Type { get; set; }
        public decimal TotalQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public decimal RemainingQuota { get; set; }
        public bool IsUnlimited { get; set; }
        public QuotaSource Source { get; set; }
        public string? SourceDescription { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsExpired { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class QuotaTransactionDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid QuotaId { get; set; }
        public QuotaTransactionType Type { get; set; }
        public decimal Amount { get; set; }
        public decimal BalanceBefore { get; set; }
        public decimal BalanceAfter { get; set; }
        public string? Description { get; set; }
        public Guid? TokenId { get; set; }
        public Guid? RequestLogId { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    // Request Log DTOs
    public class RequestLogDto
    {
        public Guid Id { get; set; }
        public Guid? UserId { get; set; }
        public Guid? TokenId { get; set; }
        public Guid? ChannelId { get; set; }
        public Guid? ModelId { get; set; }
        public string RequestPath { get; set; } = string.Empty;
        public string RequestMethod { get; set; } = string.Empty;
        public string? RequestIp { get; set; }
        public string? UserAgent { get; set; }
        public string? RequestHeaders { get; set; }
        public string? RequestBody { get; set; }
        public int ResponseStatusCode { get; set; }
        public string? ResponseHeaders { get; set; }
        public string? ResponseBody { get; set; }
        public DateTime RequestStartTime { get; set; }
        public DateTime RequestEndTime { get; set; }
        public double ResponseTime { get; set; }
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorCode { get; set; }
        public string? ModelName { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public long TotalTokens { get; set; }
        public long CacheTokens { get; set; }
        public decimal Cost { get; set; }
        public bool IsStreaming { get; set; }
        public int StreamChunks { get; set; }
        public RequestType Type { get; set; }
        public ContentType ContentType { get; set; }
        public int FileCount { get; set; }
        public long FileSize { get; set; }
        public int RetryCount { get; set; }
        public string? ChannelName { get; set; }
        public string? ChannelType { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    // Statistics DTOs
    public class SystemStatisticsDto
    {
        public long TotalUsers { get; set; }
        public long ActiveUsers { get; set; }
        public long TotalTokens { get; set; }
        public long ActiveTokens { get; set; }
        public long TotalChannels { get; set; }
        public long ActiveChannels { get; set; }
        public long TotalModels { get; set; }
        public long ActiveModels { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double OverallSuccessRate { get; set; }
        public long TotalTokensConsumed { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalRevenue { get; set; }
        public double AverageResponseTime { get; set; }
        public DateTime StatisticsTime { get; set; }
    }

    public class DashboardDto
    {
        public SystemStatisticsDto SystemStatistics { get; set; } = new();
        public List<ModelUsageDto> TopModels { get; set; } = new();
        public List<ChannelStatisticsDto> TopChannels { get; set; } = new();
        public List<UserActivityDto> RecentUsers { get; set; } = new();
        public List<RequestLogDto> RecentRequests { get; set; } = new();
        public List<DailyStatisticsDto> DailyStatistics { get; set; } = new();
    }

    public class ModelUsageDto
    {
        public Guid ModelId { get; set; }
        public string ModelName { get; set; } = string.Empty;
        public long RequestCount { get; set; }
        public long TokenCount { get; set; }
        public decimal Cost { get; set; }
        public double SuccessRate { get; set; }
    }

    public class UserActivityDto
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public long RequestCount { get; set; }
        public long TokenCount { get; set; }
        public decimal Cost { get; set; }
        public DateTime? LastActivityTime { get; set; }
    }

    public class DailyStatisticsDto
    {
        public DateTime Date { get; set; }
        public long RequestCount { get; set; }
        public long TokenCount { get; set; }
        public decimal Cost { get; set; }
        public long UserCount { get; set; }
        public double SuccessRate { get; set; }
    }

    // API Request/Response DTOs
    public class ApiRequestDto
    {
        public string Model { get; set; } = string.Empty;
        public List<MessageDto> Messages { get; set; } = new();
        public double? Temperature { get; set; }
        public int? MaxTokens { get; set; }
        public bool? Stream { get; set; }
        public Dictionary<string, object>? AdditionalProperties { get; set; }
    }

    public class MessageDto
    {
        public string Role { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string? Name { get; set; }
    }

    public class ApiResponseDto
    {
        public string Id { get; set; } = string.Empty;
        public string Object { get; set; } = string.Empty;
        public long Created { get; set; }
        public string Model { get; set; } = string.Empty;
        public List<ChoiceDto> Choices { get; set; } = new();
        public UsageDto? Usage { get; set; }
    }

    public class ChoiceDto
    {
        public int Index { get; set; }
        public MessageDto? Message { get; set; }
        public string? FinishReason { get; set; }
    }

    public class UsageDto
    {
        public int PromptTokens { get; set; }
        public int CompletionTokens { get; set; }
        public int TotalTokens { get; set; }
    }
}
