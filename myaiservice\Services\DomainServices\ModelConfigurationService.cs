using myaiservice.Entities;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using System.Text.Json;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 模型配置管理服务
    /// </summary>
    public class ModelConfigurationService : DomainService
    {
        private readonly IRepository<Model, Guid> _modelRepository;

        public ModelConfigurationService(IRepository<Model, Guid> modelRepository)
        {
            _modelRepository = modelRepository;
        }

        /// <summary>
        /// 获取模型配置
        /// </summary>
        public async Task<ModelConfiguration> GetModelConfigurationAsync(string modelName)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                throw new InvalidOperationException($"Model '{modelName}' not found");

            return new ModelConfiguration
            {
                ModelId = model.Id,
                Name = model.Name,
                DisplayName = model.DisplayName,
                Type = model.Type,
                Provider = model.Provider,
                IsEnabled = model.IsEnabled,
                InputPrice = model.InputPrice,
                OutputPrice = model.OutputPrice,
                CachePrice = model.CachePrice,
                MaxContextLength = model.MaxContextLength,
                MaxOutputLength = model.MaxOutputLength,
                SupportsStreaming = model.SupportsStreaming,
                SupportsFunctionCalling = model.SupportsFunctionCalling,
                SupportsVision = model.SupportsVision,
                SupportsAudio = model.SupportsAudio,
                SupportsImageGeneration = model.SupportsImageGeneration,
                SupportedFileTypes = ParseSupportedFileTypes(model.SupportedFileTypes),
                Description = model.Description,
                Version = model.Version,
                IsDeprecated = model.IsDeprecated,
                ReplacementModel = model.ReplacementModel
            };
        }

        /// <summary>
        /// 获取模型的参数限制
        /// </summary>
        public async Task<ModelParameterLimits> GetModelParameterLimitsAsync(string modelName)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                throw new InvalidOperationException($"Model '{modelName}' not found");

            return new ModelParameterLimits
            {
                MaxTokens = new ParameterLimit<int>
                {
                    Min = 1,
                    Max = model.MaxOutputLength,
                    Default = Math.Min(1024, model.MaxOutputLength)
                },
                Temperature = new ParameterLimit<double>
                {
                    Min = 0.0,
                    Max = 2.0,
                    Default = 0.7
                },
                TopP = new ParameterLimit<double>
                {
                    Min = 0.0,
                    Max = 1.0,
                    Default = 1.0
                },
                FrequencyPenalty = new ParameterLimit<double>
                {
                    Min = -2.0,
                    Max = 2.0,
                    Default = 0.0
                },
                PresencePenalty = new ParameterLimit<double>
                {
                    Min = -2.0,
                    Max = 2.0,
                    Default = 0.0
                },
                TopK = GetTopKLimits(model),
                RepetitionPenalty = GetRepetitionPenaltyLimits(model)
            };
        }

        /// <summary>
        /// 验证请求参数是否符合模型限制
        /// </summary>
        public async Task<ParameterValidationResult> ValidateParametersAsync(string modelName, Dictionary<string, object> parameters)
        {
            var limits = await GetModelParameterLimitsAsync(modelName);
            var result = new ParameterValidationResult { IsValid = true, Errors = new List<string>() };

            // 验证max_tokens
            if (parameters.TryGetValue("max_tokens", out var maxTokensObj) && maxTokensObj is int maxTokens)
            {
                if (maxTokens < limits.MaxTokens.Min || maxTokens > limits.MaxTokens.Max)
                {
                    result.IsValid = false;
                    result.Errors.Add($"max_tokens must be between {limits.MaxTokens.Min} and {limits.MaxTokens.Max}");
                }
            }

            // 验证temperature
            if (parameters.TryGetValue("temperature", out var temperatureObj) && temperatureObj is double temperature)
            {
                if (temperature < limits.Temperature.Min || temperature > limits.Temperature.Max)
                {
                    result.IsValid = false;
                    result.Errors.Add($"temperature must be between {limits.Temperature.Min} and {limits.Temperature.Max}");
                }
            }

            // 验证top_p
            if (parameters.TryGetValue("top_p", out var topPObj) && topPObj is double topP)
            {
                if (topP < limits.TopP.Min || topP > limits.TopP.Max)
                {
                    result.IsValid = false;
                    result.Errors.Add($"top_p must be between {limits.TopP.Min} and {limits.TopP.Max}");
                }
            }

            // 验证frequency_penalty
            if (parameters.TryGetValue("frequency_penalty", out var freqPenaltyObj) && freqPenaltyObj is double freqPenalty)
            {
                if (freqPenalty < limits.FrequencyPenalty.Min || freqPenalty > limits.FrequencyPenalty.Max)
                {
                    result.IsValid = false;
                    result.Errors.Add($"frequency_penalty must be between {limits.FrequencyPenalty.Min} and {limits.FrequencyPenalty.Max}");
                }
            }

            // 验证presence_penalty
            if (parameters.TryGetValue("presence_penalty", out var presPenaltyObj) && presPenaltyObj is double presPenalty)
            {
                if (presPenalty < limits.PresencePenalty.Min || presPenalty > limits.PresencePenalty.Max)
                {
                    result.IsValid = false;
                    result.Errors.Add($"presence_penalty must be between {limits.PresencePenalty.Min} and {limits.PresencePenalty.Max}");
                }
            }

            return result;
        }

        /// <summary>
        /// 应用默认参数
        /// </summary>
        public async Task<Dictionary<string, object>> ApplyDefaultParametersAsync(string modelName, Dictionary<string, object> parameters)
        {
            var limits = await GetModelParameterLimitsAsync(modelName);
            var result = new Dictionary<string, object>(parameters);

            // 应用默认值
            if (!result.ContainsKey("max_tokens"))
                result["max_tokens"] = limits.MaxTokens.Default;

            if (!result.ContainsKey("temperature"))
                result["temperature"] = limits.Temperature.Default;

            if (!result.ContainsKey("top_p"))
                result["top_p"] = limits.TopP.Default;

            if (!result.ContainsKey("frequency_penalty"))
                result["frequency_penalty"] = limits.FrequencyPenalty.Default;

            if (!result.ContainsKey("presence_penalty"))
                result["presence_penalty"] = limits.PresencePenalty.Default;

            return result;
        }

        /// <summary>
        /// 获取模型的功能支持信息
        /// </summary>
        public async Task<ModelCapabilities> GetModelCapabilitiesAsync(string modelName)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                throw new InvalidOperationException($"Model '{modelName}' not found");

            return new ModelCapabilities
            {
                SupportsStreaming = model.SupportsStreaming,
                SupportsFunctionCalling = model.SupportsFunctionCalling,
                SupportsVision = model.SupportsVision,
                SupportsAudio = model.SupportsAudio,
                SupportsImageGeneration = model.SupportsImageGeneration,
                SupportedInputTypes = GetSupportedInputTypes(model),
                SupportedOutputTypes = GetSupportedOutputTypes(model),
                MaxContextLength = model.MaxContextLength,
                MaxOutputLength = model.MaxOutputLength
            };
        }

        /// <summary>
        /// 解析支持的文件类型
        /// </summary>
        private Dictionary<string, List<string>> ParseSupportedFileTypes(string? supportedFileTypes)
        {
            if (string.IsNullOrEmpty(supportedFileTypes))
                return new Dictionary<string, List<string>>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, List<string>>>(supportedFileTypes) 
                       ?? new Dictionary<string, List<string>>();
            }
            catch
            {
                return new Dictionary<string, List<string>>();
            }
        }

        /// <summary>
        /// 获取TopK参数限制
        /// </summary>
        private ParameterLimit<int>? GetTopKLimits(Model model)
        {
            // 某些模型支持top_k参数
            if (model.Provider == ModelProvider.Google || model.Provider == ModelProvider.Baidu)
            {
                return new ParameterLimit<int>
                {
                    Min = 1,
                    Max = 40,
                    Default = 1
                };
            }
            return null;
        }

        /// <summary>
        /// 获取重复惩罚参数限制
        /// </summary>
        private ParameterLimit<double>? GetRepetitionPenaltyLimits(Model model)
        {
            // 某些模型支持repetition_penalty参数
            if (model.Provider == ModelProvider.Zhipu || model.Provider == ModelProvider.DeepSeek)
            {
                return new ParameterLimit<double>
                {
                    Min = 0.1,
                    Max = 2.0,
                    Default = 1.0
                };
            }
            return null;
        }

        /// <summary>
        /// 获取支持的输入类型
        /// </summary>
        private List<string> GetSupportedInputTypes(Model model)
        {
            var types = new List<string> { "text" };

            if (model.SupportsVision)
                types.Add("image");

            if (model.SupportsAudio)
                types.Add("audio");

            return types;
        }

        /// <summary>
        /// 获取支持的输出类型
        /// </summary>
        private List<string> GetSupportedOutputTypes(Model model)
        {
            var types = new List<string> { "text" };

            if (model.SupportsImageGeneration)
                types.Add("image");

            if (model.Type == ModelType.TextToSpeech)
                types.Add("audio");

            return types;
        }
    }

    // 配置相关的数据传输对象
    public class ModelConfiguration
    {
        public Guid ModelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public ModelType Type { get; set; }
        public ModelProvider Provider { get; set; }
        public bool IsEnabled { get; set; }
        public decimal InputPrice { get; set; }
        public decimal OutputPrice { get; set; }
        public decimal CachePrice { get; set; }
        public int MaxContextLength { get; set; }
        public int MaxOutputLength { get; set; }
        public bool SupportsStreaming { get; set; }
        public bool SupportsFunctionCalling { get; set; }
        public bool SupportsVision { get; set; }
        public bool SupportsAudio { get; set; }
        public bool SupportsImageGeneration { get; set; }
        public Dictionary<string, List<string>> SupportedFileTypes { get; set; } = new();
        public string? Description { get; set; }
        public string? Version { get; set; }
        public bool IsDeprecated { get; set; }
        public string? ReplacementModel { get; set; }
    }

    public class ModelParameterLimits
    {
        public ParameterLimit<int> MaxTokens { get; set; } = new();
        public ParameterLimit<double> Temperature { get; set; } = new();
        public ParameterLimit<double> TopP { get; set; } = new();
        public ParameterLimit<double> FrequencyPenalty { get; set; } = new();
        public ParameterLimit<double> PresencePenalty { get; set; } = new();
        public ParameterLimit<int>? TopK { get; set; }
        public ParameterLimit<double>? RepetitionPenalty { get; set; }
    }

    public class ParameterLimit<T>
    {
        public T Min { get; set; } = default!;
        public T Max { get; set; } = default!;
        public T Default { get; set; } = default!;
    }

    public class ParameterValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class ModelCapabilities
    {
        public bool SupportsStreaming { get; set; }
        public bool SupportsFunctionCalling { get; set; }
        public bool SupportsVision { get; set; }
        public bool SupportsAudio { get; set; }
        public bool SupportsImageGeneration { get; set; }
        public List<string> SupportedInputTypes { get; set; } = new();
        public List<string> SupportedOutputTypes { get; set; } = new();
        public int MaxContextLength { get; set; }
        public int MaxOutputLength { get; set; }
    }
}
