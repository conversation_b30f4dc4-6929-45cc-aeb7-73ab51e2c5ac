using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// Token管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TokenManagementController : AbpControllerBase
    {
        private readonly TokenManagementAppService _tokenManagementAppService;

        public TokenManagementController(TokenManagementAppService tokenManagementAppService)
        {
            _tokenManagementAppService = tokenManagementAppService;
        }

        /// <summary>
        /// 创建Token
        /// </summary>
        [HttpPost]
        public async Task<TokenDto> CreateTokenAsync([FromBody] CreateTokenDto input)
        {
            return await _tokenManagementAppService.CreateTokenAsync(input);
        }

        /// <summary>
        /// 获取当前用户的Token列表
        /// </summary>
        [HttpGet]
        public async Task<List<TokenDto>> GetMyTokensAsync()
        {
            return await _tokenManagementAppService.GetUserTokensAsync();
        }

        /// <summary>
        /// 获取Token详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<TokenDto> GetTokenAsync(Guid id)
        {
            return await _tokenManagementAppService.GetTokenAsync(id);
        }

        /// <summary>
        /// 更新Token
        /// </summary>
        [HttpPut("{id}")]
        public async Task<TokenDto> UpdateTokenAsync(Guid id, [FromBody] UpdateTokenDto input)
        {
            return await _tokenManagementAppService.UpdateTokenAsync(id, input);
        }

        /// <summary>
        /// 删除Token
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTokenAsync(Guid id)
        {
            await _tokenManagementAppService.DeleteTokenAsync(id);
            return Ok();
        }

        /// <summary>
        /// 启用/禁用Token
        /// </summary>
        [HttpPut("{id}/status")]
        public async Task<IActionResult> SetTokenStatusAsync(Guid id, [FromBody] SetTokenStatusRequest request)
        {
            await _tokenManagementAppService.SetTokenStatusAsync(id, request.IsEnabled);
            return Ok();
        }

        /// <summary>
        /// 为Token添加配额
        /// </summary>
        [HttpPost("{id}/quota")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> AddTokenQuotaAsync(Guid id, [FromBody] AddQuotaRequest request)
        {
            await _tokenManagementAppService.AddTokenQuotaAsync(id, request.Amount);
            return Ok();
        }

        /// <summary>
        /// 重置Token统计
        /// </summary>
        [HttpPost("{id}/reset-statistics")]
        public async Task<IActionResult> ResetTokenStatisticsAsync(Guid id)
        {
            await _tokenManagementAppService.ResetTokenStatisticsAsync(id);
            return Ok();
        }

        /// <summary>
        /// 获取Token统计信息
        /// </summary>
        [HttpGet("{id}/statistics")]
        public async Task<TokenStatisticsDto> GetTokenStatisticsAsync(Guid id)
        {
            return await _tokenManagementAppService.GetTokenStatisticsAsync(id);
        }

        /// <summary>
        /// 验证Token (内部API)
        /// </summary>
        [HttpPost("validate")]
        [AllowAnonymous]
        public async Task<TokenValidationResult> ValidateTokenAsync([FromBody] ValidateTokenRequest request)
        {
            return await _tokenManagementAppService.ValidateTokenAsync(
                request.TokenValue, 
                request.IpAddress, 
                request.ModelName);
        }
    }

    // Request DTOs
    public class SetTokenStatusRequest
    {
        public bool IsEnabled { get; set; }
    }

    public class AddQuotaRequest
    {
        public decimal Amount { get; set; }
    }

    public class ValidateTokenRequest
    {
        public string TokenValue { get; set; } = string.Empty;
        public string? IpAddress { get; set; }
        public string? ModelName { get; set; }
    }
}
