using myaiservice.Entities;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using System.Text.Json;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 模型映射服务 - 处理不同AI服务商之间的模型映射和参数转换
    /// </summary>
    public class ModelMappingService : DomainService
    {
        private readonly IRepository<Model, Guid> _modelRepository;
        private readonly IRepository<Channel, Guid> _channelRepository;

        public ModelMappingService(
            IRepository<Model, Guid> modelRepository,
            IRepository<Channel, Guid> channelRepository)
        {
            _modelRepository = modelRepository;
            _channelRepository = channelRepository;
        }

        /// <summary>
        /// 获取渠道支持的模型映射
        /// </summary>
        public async Task<string?> GetChannelModelMappingAsync(Guid channelId, string requestedModel)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            if (string.IsNullOrEmpty(channel.ModelMapping))
                return requestedModel; // 没有映射配置，直接返回原模型名

            try
            {
                var mappingConfig = JsonSerializer.Deserialize<Dictionary<string, string>>(channel.ModelMapping);
                return mappingConfig?.GetValueOrDefault(requestedModel, requestedModel) ?? requestedModel;
            }
            catch
            {
                return requestedModel; // 映射配置解析失败，返回原模型名
            }
        }

        /// <summary>
        /// 检查渠道是否支持指定模型
        /// </summary>
        public async Task<bool> IsModelSupportedByChannelAsync(Guid channelId, string modelName)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            if (string.IsNullOrEmpty(channel.SupportedModels))
                return true; // 没有限制，支持所有模型

            try
            {
                var supportedModels = JsonSerializer.Deserialize<List<string>>(channel.SupportedModels);
                return supportedModels?.Contains(modelName) ?? true;
            }
            catch
            {
                return true; // 配置解析失败，默认支持
            }
        }

        /// <summary>
        /// 转换请求参数以适配不同的AI服务商
        /// </summary>
        public async Task<object> TransformRequestParametersAsync(Guid channelId, object requestBody)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            return channel.Type switch
            {
                ChannelType.OpenAI => TransformToOpenAIFormat(requestBody),
                ChannelType.AzureOpenAI => TransformToAzureOpenAIFormat(requestBody),
                ChannelType.Claude => TransformToClaudeFormat(requestBody),
                ChannelType.Gemini => TransformToGeminiFormat(requestBody),
                ChannelType.BaiduWenxin => TransformToBaiduFormat(requestBody),
                ChannelType.XunfeiSpark => TransformToXunfeiFormat(requestBody),
                ChannelType.ZhipuGLM => TransformToZhipuFormat(requestBody),
                ChannelType.MoonshotAI => TransformToMoonshotFormat(requestBody),
                ChannelType.DeepSeek => TransformToDeepSeekFormat(requestBody),
                _ => requestBody // 默认不转换
            };
        }

        /// <summary>
        /// 转换为OpenAI格式
        /// </summary>
        private object TransformToOpenAIFormat(object requestBody)
        {
            // OpenAI是标准格式，不需要转换
            return requestBody;
        }

        /// <summary>
        /// 转换为Azure OpenAI格式
        /// </summary>
        private object TransformToAzureOpenAIFormat(object requestBody)
        {
            // Azure OpenAI与OpenAI格式基本相同，只是URL不同
            return requestBody;
        }

        /// <summary>
        /// 转换为Claude格式
        /// </summary>
        private object TransformToClaudeFormat(object requestBody)
        {
            if (requestBody is JsonElement jsonElement)
            {
                var request = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                if (request == null) return requestBody;

                // Claude使用不同的参数名称
                var claudeRequest = new Dictionary<string, object>();

                // 转换模型名称
                if (request.TryGetValue("model", out var model))
                {
                    claudeRequest["model"] = model;
                }

                // 转换消息格式
                if (request.TryGetValue("messages", out var messages))
                {
                    claudeRequest["messages"] = messages;
                }

                // 转换max_tokens
                if (request.TryGetValue("max_tokens", out var maxTokens))
                {
                    claudeRequest["max_tokens"] = maxTokens;
                }
                else
                {
                    claudeRequest["max_tokens"] = 1024; // Claude要求必须有max_tokens
                }

                // 转换temperature
                if (request.TryGetValue("temperature", out var temperature))
                {
                    claudeRequest["temperature"] = temperature;
                }

                // 转换stream
                if (request.TryGetValue("stream", out var stream))
                {
                    claudeRequest["stream"] = stream;
                }

                return claudeRequest;
            }

            return requestBody;
        }

        /// <summary>
        /// 转换为Gemini格式
        /// </summary>
        private object TransformToGeminiFormat(object requestBody)
        {
            if (requestBody is JsonElement jsonElement)
            {
                var request = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                if (request == null) return requestBody;

                // Gemini使用不同的API格式
                var geminiRequest = new Dictionary<string, object>();

                // 转换消息格式为contents
                if (request.TryGetValue("messages", out var messages))
                {
                    var contents = TransformMessagesToGeminiContents(messages);
                    geminiRequest["contents"] = contents;
                }

                // 转换生成配置
                var generationConfig = new Dictionary<string, object>();
                
                if (request.TryGetValue("temperature", out var temperature))
                {
                    generationConfig["temperature"] = temperature;
                }

                if (request.TryGetValue("max_tokens", out var maxTokens))
                {
                    generationConfig["maxOutputTokens"] = maxTokens;
                }

                if (generationConfig.Count > 0)
                {
                    geminiRequest["generationConfig"] = generationConfig;
                }

                return geminiRequest;
            }

            return requestBody;
        }

        /// <summary>
        /// 转换消息格式为Gemini的contents格式
        /// </summary>
        private object TransformMessagesToGeminiContents(object messages)
        {
            // 简化实现，实际需要根据Gemini API文档进行详细转换
            return messages;
        }

        /// <summary>
        /// 转换为百度文心格式
        /// </summary>
        private object TransformToBaiduFormat(object requestBody)
        {
            if (requestBody is JsonElement jsonElement)
            {
                var request = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                if (request == null) return requestBody;

                // 百度文心使用不同的参数名称
                var baiduRequest = new Dictionary<string, object>();

                if (request.TryGetValue("messages", out var messages))
                {
                    baiduRequest["messages"] = messages;
                }

                if (request.TryGetValue("temperature", out var temperature))
                {
                    baiduRequest["temperature"] = temperature;
                }

                if (request.TryGetValue("stream", out var stream))
                {
                    baiduRequest["stream"] = stream;
                }

                return baiduRequest;
            }

            return requestBody;
        }

        /// <summary>
        /// 转换为讯飞星火格式
        /// </summary>
        private object TransformToXunfeiFormat(object requestBody)
        {
            // 讯飞星火使用WebSocket协议，需要特殊处理
            return requestBody;
        }

        /// <summary>
        /// 转换为智谱GLM格式
        /// </summary>
        private object TransformToZhipuFormat(object requestBody)
        {
            // 智谱GLM基本兼容OpenAI格式
            return requestBody;
        }

        /// <summary>
        /// 转换为Moonshot格式
        /// </summary>
        private object TransformToMoonshotFormat(object requestBody)
        {
            // Moonshot基本兼容OpenAI格式
            return requestBody;
        }

        /// <summary>
        /// 转换为DeepSeek格式
        /// </summary>
        private object TransformToDeepSeekFormat(object requestBody)
        {
            // DeepSeek基本兼容OpenAI格式
            return requestBody;
        }

        /// <summary>
        /// 转换响应格式为标准OpenAI格式
        /// </summary>
        public async Task<object> TransformResponseToStandardAsync(Guid channelId, object responseBody)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            return channel.Type switch
            {
                ChannelType.Claude => TransformClaudeResponseToStandard(responseBody),
                ChannelType.Gemini => TransformGeminiResponseToStandard(responseBody),
                ChannelType.BaiduWenxin => TransformBaiduResponseToStandard(responseBody),
                ChannelType.XunfeiSpark => TransformXunfeiResponseToStandard(responseBody),
                _ => responseBody // OpenAI兼容格式不需要转换
            };
        }

        /// <summary>
        /// 转换Claude响应为标准格式
        /// </summary>
        private object TransformClaudeResponseToStandard(object responseBody)
        {
            // 实现Claude响应格式到OpenAI格式的转换
            return responseBody;
        }

        /// <summary>
        /// 转换Gemini响应为标准格式
        /// </summary>
        private object TransformGeminiResponseToStandard(object responseBody)
        {
            // 实现Gemini响应格式到OpenAI格式的转换
            return responseBody;
        }

        /// <summary>
        /// 转换百度响应为标准格式
        /// </summary>
        private object TransformBaiduResponseToStandard(object responseBody)
        {
            // 实现百度响应格式到OpenAI格式的转换
            return responseBody;
        }

        /// <summary>
        /// 转换讯飞响应为标准格式
        /// </summary>
        private object TransformXunfeiResponseToStandard(object responseBody)
        {
            // 实现讯飞响应格式到OpenAI格式的转换
            return responseBody;
        }

        /// <summary>
        /// 获取模型的默认参数
        /// </summary>
        public async Task<ModelDefaultParameters> GetModelDefaultParametersAsync(string modelName)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                return new ModelDefaultParameters();

            return new ModelDefaultParameters
            {
                MaxTokens = model.MaxOutputLength,
                Temperature = 0.7,
                TopP = 1.0,
                FrequencyPenalty = 0.0,
                PresencePenalty = 0.0,
                SupportsStreaming = model.SupportsStreaming,
                SupportsFunctionCalling = model.SupportsFunctionCalling,
                SupportsVision = model.SupportsVision
            };
        }
    }

    /// <summary>
    /// 模型默认参数
    /// </summary>
    public class ModelDefaultParameters
    {
        public int MaxTokens { get; set; } = 2048;
        public double Temperature { get; set; } = 0.7;
        public double TopP { get; set; } = 1.0;
        public double FrequencyPenalty { get; set; } = 0.0;
        public double PresencePenalty { get; set; } = 0.0;
        public bool SupportsStreaming { get; set; } = true;
        public bool SupportsFunctionCalling { get; set; } = false;
        public bool SupportsVision { get; set; } = false;
    }
}
