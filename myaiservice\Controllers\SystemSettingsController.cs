using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 系统设置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "admin")]
    public class SystemSettingsController : AbpControllerBase
    {
        /// <summary>
        /// 获取系统设置
        /// </summary>
        [HttpGet]
        public async Task<SystemSettings> GetSystemSettingsAsync()
        {
            // 简化实现，实际应该从配置存储获取
            return await Task.FromResult(new SystemSettings
            {
                General = new GeneralSettings
                {
                    SiteName = "MyAI API Service",
                    SiteDescription = "AI API中继服务平台",
                    AdminEmail = "<EMAIL>",
                    SupportEmail = "<EMAIL>",
                    TimeZone = "Asia/Shanghai",
                    Language = "zh-CN",
                    EnableRegistration = true,
                    RequireEmailVerification = true,
                    EnableInviteSystem = true,
                    DefaultUserQuota = 10.0m
                },
                Api = new ApiSettings
                {
                    MaxRequestsPerMinute = 1000,
                    MaxTokensPerRequest = 4096,
                    DefaultTimeout = 30,
                    EnableRateLimit = true,
                    EnableCaching = true,
                    CacheTtlSeconds = 300,
                    EnableRequestLogging = true,
                    LogLevel = "Information"
                },
                Security = new SecuritySettings
                {
                    TokenExpirationDays = 365,
                    MaxFailedLoginAttempts = 5,
                    LockoutDurationMinutes = 30,
                    RequireStrongPassword = true,
                    EnableTwoFactorAuth = false,
                    AllowedIpRanges = new List<string> { "0.0.0.0/0" },
                    EnableIpWhitelist = false
                },
                Billing = new BillingSettings
                {
                    Currency = "USD",
                    DefaultInputPrice = 0.0015m,
                    DefaultOutputPrice = 0.002m,
                    MinimumCharge = 0.0001m,
                    EnableBulkDiscount = true,
                    BulkDiscountThreshold = 10000,
                    BulkDiscountRate = 0.1
                },
                Notification = new NotificationSettings
                {
                    EnableEmailNotifications = true,
                    EnableQuotaAlerts = true,
                    QuotaAlertThreshold = 0.8,
                    EnableSystemAlerts = true,
                    SmtpHost = "smtp.gmail.com",
                    SmtpPort = 587,
                    SmtpUsername = "<EMAIL>",
                    SmtpPassword = "********",
                    SmtpEnableSsl = true
                },
                Maintenance = new MaintenanceSettings
                {
                    EnableAutoBackup = true,
                    BackupIntervalHours = 24,
                    LogRetentionDays = 90,
                    EnableAutoCleanup = true,
                    CleanupIntervalDays = 7,
                    MaxDatabaseSize = 10 * 1024 * 1024 * 1024L // 10GB
                }
            });
        }

        /// <summary>
        /// 更新系统设置
        /// </summary>
        [HttpPut]
        public async Task<IActionResult> UpdateSystemSettingsAsync([FromBody] SystemSettings settings)
        {
            // 验证设置
            var validationResult = ValidateSettings(settings);
            if (!validationResult.IsValid)
            {
                return BadRequest(new { errors = validationResult.Errors });
            }

            // 简化实现，实际应该保存到配置存储
            return await Task.FromResult(Ok(new { success = true, message = "System settings updated successfully" }));
        }

        /// <summary>
        /// 获取特定分类的设置
        /// </summary>
        [HttpGet("{category}")]
        public async Task<IActionResult> GetSettingsByCategoryAsync(string category)
        {
            var allSettings = await GetSystemSettingsAsync();
            
            return category.ToLower() switch
            {
                "general" => Ok(allSettings.General),
                "api" => Ok(allSettings.Api),
                "security" => Ok(allSettings.Security),
                "billing" => Ok(allSettings.Billing),
                "notification" => Ok(allSettings.Notification),
                "maintenance" => Ok(allSettings.Maintenance),
                _ => BadRequest(new { error = "Invalid settings category" })
            };
        }

        /// <summary>
        /// 更新特定分类的设置
        /// </summary>
        [HttpPut("{category}")]
        public async Task<IActionResult> UpdateSettingsByCategoryAsync(string category, [FromBody] object settings)
        {
            // 简化实现
            return await Task.FromResult(Ok(new { success = true, message = $"{category} settings updated successfully" }));
        }

        /// <summary>
        /// 重置设置为默认值
        /// </summary>
        [HttpPost("reset/{category}")]
        public async Task<IActionResult> ResetSettingsAsync(string category)
        {
            // 简化实现
            return await Task.FromResult(Ok(new { success = true, message = $"{category} settings reset to default values" }));
        }

        /// <summary>
        /// 导出系统设置
        /// </summary>
        [HttpGet("export")]
        public async Task<IActionResult> ExportSettingsAsync()
        {
            var settings = await GetSystemSettingsAsync();
            var exportData = new
            {
                ExportedAt = DateTime.UtcNow,
                Version = "1.0.0",
                Settings = settings
            };

            return Ok(exportData);
        }

        /// <summary>
        /// 导入系统设置
        /// </summary>
        [HttpPost("import")]
        public async Task<IActionResult> ImportSettingsAsync([FromBody] ImportSettingsRequest request)
        {
            try
            {
                // 验证导入数据
                if (request.Settings == null)
                {
                    return BadRequest(new { error = "Invalid settings data" });
                }

                // 简化实现
                return await Task.FromResult(Ok(new { success = true, message = "Settings imported successfully" }));
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = $"Import failed: {ex.Message}" });
            }
        }

        /// <summary>
        /// 测试邮件配置
        /// </summary>
        [HttpPost("test-email")]
        public async Task<IActionResult> TestEmailConfigurationAsync([FromBody] TestEmailRequest request)
        {
            try
            {
                // 简化实现，实际应该发送测试邮件
                return await Task.FromResult(Ok(new { success = true, message = "Test email sent successfully" }));
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = $"Email test failed: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        [HttpGet("system-status")]
        public async Task<SystemStatus> GetSystemStatusAsync()
        {
            return await Task.FromResult(new SystemStatus
            {
                IsHealthy = true,
                Version = "1.0.0",
                Environment = "Production",
                StartTime = DateTime.UtcNow.AddDays(-15),
                Uptime = TimeSpan.FromDays(15),
                DatabaseStatus = "Connected",
                CacheStatus = "Active",
                QueueStatus = "Running",
                LastBackup = DateTime.UtcNow.AddHours(-6),
                NextBackup = DateTime.UtcNow.AddHours(18),
                TotalUsers = 1250,
                ActiveTokens = 890,
                TotalRequests = 125000,
                ErrorRate = 0.02
            });
        }

        private SettingsValidationResult ValidateSettings(SystemSettings settings)
        {
            var result = new SettingsValidationResult { IsValid = true, Errors = new List<string>() };

            // 验证通用设置
            if (string.IsNullOrEmpty(settings.General?.SiteName))
                result.Errors.Add("Site name is required");

            if (settings.General?.DefaultUserQuota <= 0)
                result.Errors.Add("Default user quota must be greater than 0");

            // 验证API设置
            if (settings.Api?.MaxRequestsPerMinute <= 0)
                result.Errors.Add("Max requests per minute must be greater than 0");

            if (settings.Api?.DefaultTimeout <= 0)
                result.Errors.Add("Default timeout must be greater than 0");

            // 验证安全设置
            if (settings.Security?.TokenExpirationDays <= 0)
                result.Errors.Add("Token expiration days must be greater than 0");

            // 验证计费设置
            if (settings.Billing?.DefaultInputPrice < 0)
                result.Errors.Add("Default input price cannot be negative");

            if (settings.Billing?.DefaultOutputPrice < 0)
                result.Errors.Add("Default output price cannot be negative");

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
    }

    // 系统设置相关的数据传输对象
    public class SystemSettings
    {
        public GeneralSettings General { get; set; } = new();
        public ApiSettings Api { get; set; } = new();
        public SecuritySettings Security { get; set; } = new();
        public BillingSettings Billing { get; set; } = new();
        public NotificationSettings Notification { get; set; } = new();
        public MaintenanceSettings Maintenance { get; set; } = new();
    }

    public class GeneralSettings
    {
        public string SiteName { get; set; } = string.Empty;
        public string SiteDescription { get; set; } = string.Empty;
        public string AdminEmail { get; set; } = string.Empty;
        public string SupportEmail { get; set; } = string.Empty;
        public string TimeZone { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public bool EnableRegistration { get; set; }
        public bool RequireEmailVerification { get; set; }
        public bool EnableInviteSystem { get; set; }
        public decimal DefaultUserQuota { get; set; }
    }

    public class ApiSettings
    {
        public int MaxRequestsPerMinute { get; set; }
        public int MaxTokensPerRequest { get; set; }
        public int DefaultTimeout { get; set; }
        public bool EnableRateLimit { get; set; }
        public bool EnableCaching { get; set; }
        public int CacheTtlSeconds { get; set; }
        public bool EnableRequestLogging { get; set; }
        public string LogLevel { get; set; } = string.Empty;
    }

    public class SecuritySettings
    {
        public int TokenExpirationDays { get; set; }
        public int MaxFailedLoginAttempts { get; set; }
        public int LockoutDurationMinutes { get; set; }
        public bool RequireStrongPassword { get; set; }
        public bool EnableTwoFactorAuth { get; set; }
        public List<string> AllowedIpRanges { get; set; } = new();
        public bool EnableIpWhitelist { get; set; }
    }

    public class BillingSettings
    {
        public string Currency { get; set; } = string.Empty;
        public decimal DefaultInputPrice { get; set; }
        public decimal DefaultOutputPrice { get; set; }
        public decimal MinimumCharge { get; set; }
        public bool EnableBulkDiscount { get; set; }
        public int BulkDiscountThreshold { get; set; }
        public double BulkDiscountRate { get; set; }
    }

    public class NotificationSettings
    {
        public bool EnableEmailNotifications { get; set; }
        public bool EnableQuotaAlerts { get; set; }
        public double QuotaAlertThreshold { get; set; }
        public bool EnableSystemAlerts { get; set; }
        public string SmtpHost { get; set; } = string.Empty;
        public int SmtpPort { get; set; }
        public string SmtpUsername { get; set; } = string.Empty;
        public string SmtpPassword { get; set; } = string.Empty;
        public bool SmtpEnableSsl { get; set; }
    }

    public class MaintenanceSettings
    {
        public bool EnableAutoBackup { get; set; }
        public int BackupIntervalHours { get; set; }
        public int LogRetentionDays { get; set; }
        public bool EnableAutoCleanup { get; set; }
        public int CleanupIntervalDays { get; set; }
        public long MaxDatabaseSize { get; set; }
    }

    public class ImportSettingsRequest
    {
        public SystemSettings Settings { get; set; } = new();
        public bool OverwriteExisting { get; set; } = false;
    }

    public class TestEmailRequest
    {
        [Required]
        [EmailAddress]
        public string ToEmail { get; set; } = string.Empty;
        public string Subject { get; set; } = "Test Email";
        public string Body { get; set; } = "This is a test email from MyAI system.";
    }

    public class SystemStatus
    {
        public bool IsHealthy { get; set; }
        public string Version { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime { get; set; }
        public string DatabaseStatus { get; set; } = string.Empty;
        public string CacheStatus { get; set; } = string.Empty;
        public string QueueStatus { get; set; } = string.Empty;
        public DateTime? LastBackup { get; set; }
        public DateTime? NextBackup { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveTokens { get; set; }
        public long TotalRequests { get; set; }
        public double ErrorRate { get; set; }
    }

    public class SettingsValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
