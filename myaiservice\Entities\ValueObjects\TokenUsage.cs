using Volo.Abp.Domain.Values;

namespace myaiservice.Entities.ValueObjects
{
    /// <summary>
    /// Token使用情况值对象
    /// </summary>
    public class TokenUsage : ValueObject
    {
        /// <summary>
        /// 输入Token数量
        /// </summary>
        public long InputTokens { get; private set; }

        /// <summary>
        /// 输出Token数量
        /// </summary>
        public long OutputTokens { get; private set; }

        /// <summary>
        /// 缓存Token数量
        /// </summary>
        public long CacheTokens { get; private set; }

        /// <summary>
        /// 总Token数量
        /// </summary>
        public long TotalTokens => InputTokens + OutputTokens;

        private TokenUsage()
        {
            // 用于序列化
        }

        public TokenUsage(long inputTokens, long outputTokens, long cacheTokens = 0)
        {
            if (inputTokens < 0)
                throw new ArgumentException("Input tokens cannot be negative", nameof(inputTokens));
            if (outputTokens < 0)
                throw new ArgumentException("Output tokens cannot be negative", nameof(outputTokens));
            if (cacheTokens < 0)
                throw new ArgumentException("Cache tokens cannot be negative", nameof(cacheTokens));

            InputTokens = inputTokens;
            OutputTokens = outputTokens;
            CacheTokens = cacheTokens;
        }

        /// <summary>
        /// 创建空的Token使用情况
        /// </summary>
        public static TokenUsage Empty => new(0, 0, 0);

        /// <summary>
        /// 添加Token使用量
        /// </summary>
        public TokenUsage Add(TokenUsage other)
        {
            return new TokenUsage(
                InputTokens + other.InputTokens,
                OutputTokens + other.OutputTokens,
                CacheTokens + other.CacheTokens
            );
        }

        /// <summary>
        /// 计算成本
        /// </summary>
        public decimal CalculateCost(decimal inputPrice, decimal outputPrice, decimal cachePrice = 0)
        {
            var inputCost = (decimal)InputTokens / 1000 * inputPrice;
            var outputCost = (decimal)OutputTokens / 1000 * outputPrice;
            var cacheCost = (decimal)CacheTokens / 1000 * cachePrice;
            
            return inputCost + outputCost + cacheCost;
        }

        protected override IEnumerable<object> GetAtomicValues()
        {
            yield return InputTokens;
            yield return OutputTokens;
            yield return CacheTokens;
        }

        public override string ToString()
        {
            return $"Input: {InputTokens}, Output: {OutputTokens}, Cache: {CacheTokens}, Total: {TotalTokens}";
        }
    }

    /// <summary>
    /// 价格信息值对象
    /// </summary>
    public class PriceInfo : ValueObject
    {
        /// <summary>
        /// 输入价格 (每1K tokens)
        /// </summary>
        public decimal InputPrice { get; private set; }

        /// <summary>
        /// 输出价格 (每1K tokens)
        /// </summary>
        public decimal OutputPrice { get; private set; }

        /// <summary>
        /// 缓存价格 (每1K tokens)
        /// </summary>
        public decimal CachePrice { get; private set; }

        private PriceInfo()
        {
            // 用于序列化
        }

        public PriceInfo(decimal inputPrice, decimal outputPrice, decimal cachePrice = 0)
        {
            if (inputPrice < 0)
                throw new ArgumentException("Input price cannot be negative", nameof(inputPrice));
            if (outputPrice < 0)
                throw new ArgumentException("Output price cannot be negative", nameof(outputPrice));
            if (cachePrice < 0)
                throw new ArgumentException("Cache price cannot be negative", nameof(cachePrice));

            InputPrice = inputPrice;
            OutputPrice = outputPrice;
            CachePrice = cachePrice;
        }

        /// <summary>
        /// 免费价格
        /// </summary>
        public static PriceInfo Free => new(0, 0, 0);

        /// <summary>
        /// 计算Token使用成本
        /// </summary>
        public decimal CalculateCost(TokenUsage tokenUsage)
        {
            return tokenUsage.CalculateCost(InputPrice, OutputPrice, CachePrice);
        }

        protected override IEnumerable<object> GetAtomicValues()
        {
            yield return InputPrice;
            yield return OutputPrice;
            yield return CachePrice;
        }

        public override string ToString()
        {
            return $"Input: ${InputPrice}/1K, Output: ${OutputPrice}/1K, Cache: ${CachePrice}/1K";
        }
    }

    /// <summary>
    /// 限流配置值对象
    /// </summary>
    public class RateLimitConfig : ValueObject
    {
        /// <summary>
        /// 每分钟请求限制
        /// </summary>
        public int RequestsPerMinute { get; private set; }

        /// <summary>
        /// 每小时请求限制
        /// </summary>
        public int RequestsPerHour { get; private set; }

        /// <summary>
        /// 每天请求限制
        /// </summary>
        public int RequestsPerDay { get; private set; }

        private RateLimitConfig()
        {
            // 用于序列化
        }

        public RateLimitConfig(int requestsPerMinute, int requestsPerHour, int requestsPerDay)
        {
            if (requestsPerMinute <= 0)
                throw new ArgumentException("Requests per minute must be positive", nameof(requestsPerMinute));
            if (requestsPerHour <= 0)
                throw new ArgumentException("Requests per hour must be positive", nameof(requestsPerHour));
            if (requestsPerDay <= 0)
                throw new ArgumentException("Requests per day must be positive", nameof(requestsPerDay));

            RequestsPerMinute = requestsPerMinute;
            RequestsPerHour = requestsPerHour;
            RequestsPerDay = requestsPerDay;
        }

        /// <summary>
        /// 默认限流配置
        /// </summary>
        public static RateLimitConfig Default => new(60, 3600, 86400);

        /// <summary>
        /// 无限制配置
        /// </summary>
        public static RateLimitConfig Unlimited => new(int.MaxValue, int.MaxValue, int.MaxValue);

        /// <summary>
        /// 检查是否超过限制
        /// </summary>
        public bool IsExceeded(int currentRequests, TimeSpan period)
        {
            return period.TotalMinutes <= 1 && currentRequests >= RequestsPerMinute ||
                   period.TotalHours <= 1 && currentRequests >= RequestsPerHour ||
                   period.TotalDays <= 1 && currentRequests >= RequestsPerDay;
        }

        protected override IEnumerable<object> GetAtomicValues()
        {
            yield return RequestsPerMinute;
            yield return RequestsPerHour;
            yield return RequestsPerDay;
        }

        public override string ToString()
        {
            return $"Per Minute: {RequestsPerMinute}, Per Hour: {RequestsPerHour}, Per Day: {RequestsPerDay}";
        }
    }

    /// <summary>
    /// IP地址值对象
    /// </summary>
    public class IpAddress : ValueObject
    {
        /// <summary>
        /// IP地址字符串
        /// </summary>
        public string Value { get; private set; }

        /// <summary>
        /// 是否为IPv4
        /// </summary>
        public bool IsIPv4 { get; private set; }

        /// <summary>
        /// 是否为IPv6
        /// </summary>
        public bool IsIPv6 { get; private set; }

        private IpAddress()
        {
            Value = string.Empty;
        }

        public IpAddress(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new ArgumentException("IP address cannot be null or empty", nameof(ipAddress));

            Value = ipAddress.Trim();
            
            // 简单的IP地址格式验证
            IsIPv4 = System.Net.IPAddress.TryParse(Value, out var parsedIp) && 
                     parsedIp.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork;
            IsIPv6 = System.Net.IPAddress.TryParse(Value, out parsedIp) && 
                     parsedIp.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6;

            if (!IsIPv4 && !IsIPv6)
                throw new ArgumentException($"Invalid IP address format: {ipAddress}", nameof(ipAddress));
        }

        /// <summary>
        /// 本地回环地址
        /// </summary>
        public static IpAddress Localhost => new("127.0.0.1");

        /// <summary>
        /// 检查是否为私有IP地址
        /// </summary>
        public bool IsPrivate()
        {
            if (!IsIPv4) return false;

            var parts = Value.Split('.');
            if (parts.Length != 4) return false;

            if (!int.TryParse(parts[0], out var firstOctet)) return false;

            // 私有IP地址范围
            return firstOctet == 10 ||
                   (firstOctet == 172 && int.TryParse(parts[1], out var secondOctet) && secondOctet >= 16 && secondOctet <= 31) ||
                   (firstOctet == 192 && parts[1] == "168");
        }

        protected override IEnumerable<object> GetAtomicValues()
        {
            yield return Value;
        }

        public override string ToString()
        {
            return Value;
        }

        public static implicit operator string(IpAddress ipAddress)
        {
            return ipAddress.Value;
        }

        public static implicit operator IpAddress(string ipAddress)
        {
            return new IpAddress(ipAddress);
        }
    }
}
