using AutoMapper;
using myaiservice.Entities;
using myaiservice.Services.ApplicationServices;
using myaiservice.Services.DomainServices;

namespace myaiservice.ObjectMapping;

public class myaiserviceAutoMapperProfile : Profile
{
    public myaiserviceAutoMapperProfile()
    {
        /* Create your AutoMapper object mappings here */

        // User Management mappings
        CreateMap<UserProfile, UserProfileDto>();
        CreateMap<InvitationCode, InvitationCodeDto>();

        // Token Management mappings
        CreateMap<Token, TokenDto>();
        CreateMap<CreateTokenDto, Token>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Value, opt => opt.Ignore());

        // Channel Management mappings
        CreateMap<Channel, ChannelDto>();
        CreateMap<CreateChannelDto, Channel>()
            .ForMember(dest => dest.Id, opt => opt.Ignore());
        CreateMap<UpdateChannelDto, Channel>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreationTime, opt => opt.Ignore());

        // Model mappings
        CreateMap<Model, ModelDto>();
        CreateMap<CreateModelDto, Model>()
            .ForMember(dest => dest.Id, opt => opt.Ignore());

        // Quota mappings
        CreateMap<Quota, QuotaDto>();
        CreateMap<QuotaTransaction, QuotaTransactionDto>();

        // Request Log mappings
        CreateMap<RequestLog, RequestLogDto>();

        // Statistics mappings
        CreateMap<ChannelStatistics, ChannelStatisticsDto>();
        CreateMap<TokenStatistics, TokenStatisticsDto>();
        CreateMap<ChannelHealthStatus, ChannelHealthStatusDto>();
    }
}