{"id": "b72ce9f6-8780-46fb-b7fa-5f1e581ce296", "template": "app-nolayers", "versions": {"AbpFramework": "9.1.0", "AbpStudio": "0.9.25", "TargetDotnetFramework": "net9.0"}, "modules": {"myaiservice": {"path": "myaiservice.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": []}}, "creatingStudioConfiguration": {"template": "app-nolayers", "createdAbpStudioVersion": "0.9.25", "multiTenancy": "true", "runInstallLibs": "true", "useLocalReferences": "false", "uiFramework": "no-ui", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "createInitialMigration": "true", "theme": "leptonx-lite", "themeStyle": "", "publicWebsite": "", "socialLogin": ""}}