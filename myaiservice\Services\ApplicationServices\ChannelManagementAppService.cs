using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 渠道管理应用服务
    /// </summary>
    [Authorize(Roles = "admin")]
    public class ChannelManagementAppService : ApplicationService
    {
        private readonly IRepository<Channel, Guid> _channelRepository;
        private readonly ChannelDomainService _channelDomainService;

        public ChannelManagementAppService(
            IRepository<Channel, Guid> channelRepository,
            ChannelDomainService channelDomainService)
        {
            _channelRepository = channelRepository;
            _channelDomainService = channelDomainService;
        }

        /// <summary>
        /// 创建渠道
        /// </summary>
        public async Task<ChannelDto> CreateChannelAsync(CreateChannelDto input)
        {
            var channel = await _channelDomainService.CreateChannelAsync(input.Name, input.Type, input.BaseUrl, input.ApiKey);
            
            // 设置其他属性
            channel.OrganizationId = input.OrganizationId;
            channel.ProjectId = input.ProjectId;
            channel.Priority = input.Priority;
            channel.Weight = input.Weight;
            channel.SupportedModels = input.SupportedModels;
            channel.ModelMapping = input.ModelMapping;
            channel.Config = input.Config;
            channel.Group = input.Group;
            channel.MaxConcurrency = input.MaxConcurrency;
            channel.TimeoutSeconds = input.TimeoutSeconds;
            channel.RetryCount = input.RetryCount;
            channel.AutoCheckBalance = input.AutoCheckBalance;
            channel.BalanceCheckInterval = input.BalanceCheckInterval;
            channel.MaxFailures = input.MaxFailures;
            channel.Remarks = input.Remarks;

            await _channelRepository.InsertAsync(channel);

            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// 获取所有渠道
        /// </summary>
        public async Task<List<ChannelDto>> GetChannelsAsync()
        {
            var channels = await _channelRepository.GetListAsync();
            return ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels);
        }

        /// <summary>
        /// 获取渠道详情
        /// </summary>
        public async Task<ChannelDto> GetChannelAsync(Guid id)
        {
            var channel = await _channelRepository.GetAsync(id);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// 更新渠道
        /// </summary>
        public async Task<ChannelDto> UpdateChannelAsync(Guid id, UpdateChannelDto input)
        {
            var channel = await _channelRepository.GetAsync(id);

            channel.Name = input.Name ?? channel.Name;
            channel.BaseUrl = input.BaseUrl ?? channel.BaseUrl;
            channel.ApiKey = input.ApiKey ?? channel.ApiKey;
            channel.OrganizationId = input.OrganizationId ?? channel.OrganizationId;
            channel.ProjectId = input.ProjectId ?? channel.ProjectId;
            channel.Priority = input.Priority ?? channel.Priority;
            channel.Weight = input.Weight ?? channel.Weight;
            channel.SupportedModels = input.SupportedModels ?? channel.SupportedModels;
            channel.ModelMapping = input.ModelMapping ?? channel.ModelMapping;
            channel.Config = input.Config ?? channel.Config;
            channel.Group = input.Group ?? channel.Group;
            channel.MaxConcurrency = input.MaxConcurrency ?? channel.MaxConcurrency;
            channel.TimeoutSeconds = input.TimeoutSeconds ?? channel.TimeoutSeconds;
            channel.RetryCount = input.RetryCount ?? channel.RetryCount;
            channel.AutoCheckBalance = input.AutoCheckBalance ?? channel.AutoCheckBalance;
            channel.BalanceCheckInterval = input.BalanceCheckInterval ?? channel.BalanceCheckInterval;
            channel.MaxFailures = input.MaxFailures ?? channel.MaxFailures;
            channel.Remarks = input.Remarks ?? channel.Remarks;

            await _channelRepository.UpdateAsync(channel);

            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// 删除渠道
        /// </summary>
        public async Task DeleteChannelAsync(Guid id)
        {
            await _channelRepository.DeleteAsync(id);
        }

        /// <summary>
        /// 启用渠道
        /// </summary>
        public async Task EnableChannelAsync(Guid id)
        {
            await _channelDomainService.EnableChannelAsync(id);
        }

        /// <summary>
        /// 禁用渠道
        /// </summary>
        public async Task DisableChannelAsync(Guid id)
        {
            await _channelDomainService.DisableChannelAsync(id);
        }

        /// <summary>
        /// 更新渠道余额
        /// </summary>
        public async Task UpdateChannelBalanceAsync(Guid id, decimal balance)
        {
            await _channelDomainService.UpdateChannelBalanceAsync(id, balance);
        }

        /// <summary>
        /// 执行渠道健康检查
        /// </summary>
        public async Task<ChannelHealthStatusDto> PerformHealthCheckAsync(Guid id)
        {
            var healthStatus = await _channelDomainService.PerformHealthCheckAsync(id);
            return ObjectMapper.Map<ChannelHealthStatus, ChannelHealthStatusDto>(healthStatus);
        }

        /// <summary>
        /// 批量健康检查
        /// </summary>
        public async Task<List<ChannelHealthStatusDto>> PerformBatchHealthCheckAsync()
        {
            var healthStatuses = await _channelDomainService.PerformBatchHealthCheckAsync();
            return ObjectMapper.Map<List<ChannelHealthStatus>, List<ChannelHealthStatusDto>>(healthStatuses);
        }

        /// <summary>
        /// 重置渠道失败计数
        /// </summary>
        public async Task ResetChannelFailureCountAsync(Guid id)
        {
            await _channelDomainService.ResetChannelFailureCountAsync(id);
        }

        /// <summary>
        /// 获取渠道统计信息
        /// </summary>
        public async Task<ChannelStatisticsDto> GetChannelStatisticsAsync(Guid id)
        {
            var statistics = await _channelDomainService.GetChannelStatisticsAsync(id);
            return ObjectMapper.Map<ChannelStatistics, ChannelStatisticsDto>(statistics);
        }

        /// <summary>
        /// 获取可用渠道列表
        /// </summary>
        [AllowAnonymous]
        public async Task<List<ChannelDto>> GetAvailableChannelsAsync(string? modelName = null, string? group = null)
        {
            var channels = await _channelDomainService.GetAvailableChannelsAsync(modelName, group);
            return ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels);
        }

        /// <summary>
        /// 选择最佳渠道
        /// </summary>
        [AllowAnonymous]
        public async Task<ChannelDto?> SelectBestChannelAsync(string? modelName = null, string? group = null)
        {
            var channel = await _channelDomainService.SelectBestChannelAsync(modelName, group);
            return channel != null ? ObjectMapper.Map<Channel, ChannelDto>(channel) : null;
        }
    }

    // DTOs
    public class CreateChannelDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public ChannelType Type { get; set; }

        [Required]
        [StringLength(500)]
        public string BaseUrl { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string ApiKey { get; set; } = string.Empty;

        public string? OrganizationId { get; set; }

        public string? ProjectId { get; set; }

        public int Priority { get; set; } = 0;

        public int Weight { get; set; } = 1;

        public string? SupportedModels { get; set; }

        public string? ModelMapping { get; set; }

        public string? Config { get; set; }

        public string? Group { get; set; }

        public int MaxConcurrency { get; set; } = 10;

        public int TimeoutSeconds { get; set; } = 30;

        public int RetryCount { get; set; } = 3;

        public bool AutoCheckBalance { get; set; } = true;

        public int BalanceCheckInterval { get; set; } = 60;

        public int MaxFailures { get; set; } = 10;

        public string? Remarks { get; set; }
    }

    public class UpdateChannelDto
    {
        public string? Name { get; set; }

        public string? BaseUrl { get; set; }

        public string? ApiKey { get; set; }

        public string? OrganizationId { get; set; }

        public string? ProjectId { get; set; }

        public int? Priority { get; set; }

        public int? Weight { get; set; }

        public string? SupportedModels { get; set; }

        public string? ModelMapping { get; set; }

        public string? Config { get; set; }

        public string? Group { get; set; }

        public int? MaxConcurrency { get; set; }

        public int? TimeoutSeconds { get; set; }

        public int? RetryCount { get; set; }

        public bool? AutoCheckBalance { get; set; }

        public int? BalanceCheckInterval { get; set; }

        public int? MaxFailures { get; set; }

        public string? Remarks { get; set; }
    }

    public class ChannelDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public ChannelType Type { get; set; }
        public ChannelStatus Status { get; set; }
        public string BaseUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string? OrganizationId { get; set; }
        public string? ProjectId { get; set; }
        public int Priority { get; set; }
        public int Weight { get; set; }
        public string? SupportedModels { get; set; }
        public string? ModelMapping { get; set; }
        public string? Config { get; set; }
        public string? Group { get; set; }
        public int MaxConcurrency { get; set; }
        public int TimeoutSeconds { get; set; }
        public int RetryCount { get; set; }
        public decimal Balance { get; set; }
        public DateTime? BalanceUpdatedAt { get; set; }
        public bool AutoCheckBalance { get; set; }
        public int BalanceCheckInterval { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public DateTime? LastCheckedTime { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public int FailureCount { get; set; }
        public int MaxFailures { get; set; }
        public double AverageResponseTime { get; set; }
        public string? LastError { get; set; }
        public DateTime? LastErrorTime { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class ChannelStatisticsDto
    {
        public Guid ChannelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public ChannelType Type { get; set; }
        public ChannelStatus Status { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public double AverageResponseTime { get; set; }
        public decimal Balance { get; set; }
        public int FailureCount { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public DateTime? LastCheckedTime { get; set; }
    }

    public class ChannelHealthStatusDto
    {
        public Guid ChannelId { get; set; }
        public bool IsHealthy { get; set; }
        public DateTime CheckTime { get; set; }
        public double ResponseTime { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
