using myaiservice.Entities;
using myaiservice.Entities.ValueObjects;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using System.Text.Json;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 多模态服务 - 处理图像、音频等多媒体内容
    /// </summary>
    public class MultimodalService : DomainService
    {
        private readonly IRepository<Model, Guid> _modelRepository;

        public MultimodalService(IRepository<Model, Guid> modelRepository)
        {
            _modelRepository = modelRepository;
        }

        /// <summary>
        /// 检查模型是否支持指定的多模态功能
        /// </summary>
        public async Task<bool> CheckModelSupportsFeatureAsync(string modelName, ModelFeature feature)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                return false;

            return model.SupportsFeature(feature);
        }

        /// <summary>
        /// 处理图像输入
        /// </summary>
        public async Task<ProcessedImageInput> ProcessImageInputAsync(string modelName, IFormFile imageFile)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null || !model.SupportsVision)
                throw new InvalidOperationException($"Model '{modelName}' does not support vision");

            // 验证文件类型
            var supportedTypes = GetSupportedImageTypes(model);
            if (!IsImageTypeSupported(imageFile.ContentType, supportedTypes))
                throw new InvalidOperationException($"Unsupported image type: {imageFile.ContentType}");

            // 验证文件大小
            var maxSize = GetMaxImageSize(model);
            if (imageFile.Length > maxSize)
                throw new InvalidOperationException($"Image file too large. Max size: {maxSize / 1024 / 1024}MB");

            // 读取并编码图像
            using var memoryStream = new MemoryStream();
            await imageFile.CopyToAsync(memoryStream);
            var imageBytes = memoryStream.ToArray();
            var base64Image = Convert.ToBase64String(imageBytes);

            return new ProcessedImageInput
            {
                FileName = imageFile.FileName,
                ContentType = imageFile.ContentType,
                Size = imageFile.Length,
                Base64Data = base64Image,
                DataUrl = $"data:{imageFile.ContentType};base64,{base64Image}"
            };
        }

        /// <summary>
        /// 处理音频输入
        /// </summary>
        public async Task<ProcessedAudioInput> ProcessAudioInputAsync(string modelName, IFormFile audioFile)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null || !model.SupportsAudio)
                throw new InvalidOperationException($"Model '{modelName}' does not support audio");

            // 验证文件类型
            var supportedTypes = GetSupportedAudioTypes(model);
            if (!IsAudioTypeSupported(audioFile.ContentType, supportedTypes))
                throw new InvalidOperationException($"Unsupported audio type: {audioFile.ContentType}");

            // 验证文件大小
            var maxSize = GetMaxAudioSize(model);
            if (audioFile.Length > maxSize)
                throw new InvalidOperationException($"Audio file too large. Max size: {maxSize / 1024 / 1024}MB");

            // 读取音频文件
            using var memoryStream = new MemoryStream();
            await audioFile.CopyToAsync(memoryStream);
            var audioBytes = memoryStream.ToArray();

            return new ProcessedAudioInput
            {
                FileName = audioFile.FileName,
                ContentType = audioFile.ContentType,
                Size = audioFile.Length,
                AudioData = audioBytes,
                Duration = await GetAudioDurationAsync(audioBytes, audioFile.ContentType)
            };
        }

        /// <summary>
        /// 构建多模态消息
        /// </summary>
        public async Task<object> BuildMultimodalMessageAsync(string modelName, string textContent, List<ProcessedImageInput>? images = null, ProcessedAudioInput? audio = null)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                throw new InvalidOperationException($"Model '{modelName}' not found");

            var message = new Dictionary<string, object>
            {
                ["role"] = "user"
            };

            var content = new List<object>();

            // 添加文本内容
            if (!string.IsNullOrEmpty(textContent))
            {
                content.Add(new
                {
                    type = "text",
                    text = textContent
                });
            }

            // 添加图像内容
            if (images != null && images.Count > 0 && model.SupportsVision)
            {
                foreach (var image in images)
                {
                    content.Add(new
                    {
                        type = "image_url",
                        image_url = new
                        {
                            url = image.DataUrl,
                            detail = "auto"
                        }
                    });
                }
            }

            // 添加音频内容
            if (audio != null && model.SupportsAudio)
            {
                content.Add(new
                {
                    type = "audio",
                    audio = new
                    {
                        data = Convert.ToBase64String(audio.AudioData),
                        format = GetAudioFormat(audio.ContentType)
                    }
                });
            }

            message["content"] = content;
            return message;
        }

        /// <summary>
        /// 估算多模态内容的Token消耗
        /// </summary>
        public async Task<TokenUsage> EstimateMultimodalTokenUsageAsync(string modelName, string textContent, List<ProcessedImageInput>? images = null, ProcessedAudioInput? audio = null)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == modelName);
            if (model == null)
                return TokenUsage.Empty;

            long inputTokens = 0;

            // 估算文本Token
            if (!string.IsNullOrEmpty(textContent))
            {
                inputTokens += EstimateTextTokens(textContent);
            }

            // 估算图像Token
            if (images != null && images.Count > 0 && model.SupportsVision)
            {
                foreach (var image in images)
                {
                    inputTokens += EstimateImageTokens(image, model);
                }
            }

            // 估算音频Token
            if (audio != null && model.SupportsAudio)
            {
                inputTokens += EstimateAudioTokens(audio, model);
            }

            return new TokenUsage(inputTokens, 0);
        }

        /// <summary>
        /// 获取支持的图像类型
        /// </summary>
        private List<string> GetSupportedImageTypes(Model model)
        {
            if (!string.IsNullOrEmpty(model.SupportedFileTypes))
            {
                try
                {
                    var fileTypes = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(model.SupportedFileTypes);
                    return fileTypes?.GetValueOrDefault("image", new List<string>()) ?? new List<string>();
                }
                catch
                {
                    // 解析失败，返回默认支持的类型
                }
            }

            return new List<string> { "image/jpeg", "image/png", "image/gif", "image/webp" };
        }

        /// <summary>
        /// 获取支持的音频类型
        /// </summary>
        private List<string> GetSupportedAudioTypes(Model model)
        {
            if (!string.IsNullOrEmpty(model.SupportedFileTypes))
            {
                try
                {
                    var fileTypes = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(model.SupportedFileTypes);
                    return fileTypes?.GetValueOrDefault("audio", new List<string>()) ?? new List<string>();
                }
                catch
                {
                    // 解析失败，返回默认支持的类型
                }
            }

            return new List<string> { "audio/mpeg", "audio/wav", "audio/mp4", "audio/webm" };
        }

        /// <summary>
        /// 检查图像类型是否支持
        /// </summary>
        private bool IsImageTypeSupported(string contentType, List<string> supportedTypes)
        {
            return supportedTypes.Contains(contentType);
        }

        /// <summary>
        /// 检查音频类型是否支持
        /// </summary>
        private bool IsAudioTypeSupported(string contentType, List<string> supportedTypes)
        {
            return supportedTypes.Contains(contentType);
        }

        /// <summary>
        /// 获取最大图像大小
        /// </summary>
        private long GetMaxImageSize(Model model)
        {
            return 20 * 1024 * 1024; // 默认20MB
        }

        /// <summary>
        /// 获取最大音频大小
        /// </summary>
        private long GetMaxAudioSize(Model model)
        {
            return 25 * 1024 * 1024; // 默认25MB
        }

        /// <summary>
        /// 获取音频时长
        /// </summary>
        private async Task<TimeSpan> GetAudioDurationAsync(byte[] audioData, string contentType)
        {
            // 简化实现，实际需要使用音频处理库来获取准确时长
            return TimeSpan.FromSeconds(audioData.Length / 16000.0); // 假设16kHz采样率
        }

        /// <summary>
        /// 获取音频格式
        /// </summary>
        private string GetAudioFormat(string contentType)
        {
            return contentType switch
            {
                "audio/mpeg" => "mp3",
                "audio/wav" => "wav",
                "audio/mp4" => "mp4",
                "audio/webm" => "webm",
                _ => "unknown"
            };
        }

        /// <summary>
        /// 估算文本Token数量
        /// </summary>
        private long EstimateTextTokens(string text)
        {
            // 简化估算：4个字符约等于1个Token
            return text.Length / 4;
        }

        /// <summary>
        /// 估算图像Token数量
        /// </summary>
        private long EstimateImageTokens(ProcessedImageInput image, Model model)
        {
            // 根据图像大小估算Token消耗
            // 这是一个简化的估算，实际Token消耗取决于图像分辨率和模型
            return 85; // OpenAI GPT-4V的基础Token消耗
        }

        /// <summary>
        /// 估算音频Token数量
        /// </summary>
        private long EstimateAudioTokens(ProcessedAudioInput audio, Model model)
        {
            // 根据音频时长估算Token消耗
            // Whisper模型大约每秒音频消耗1个Token
            return (long)audio.Duration.TotalSeconds;
        }
    }

    /// <summary>
    /// 处理后的图像输入
    /// </summary>
    public class ProcessedImageInput
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public string Base64Data { get; set; } = string.Empty;
        public string DataUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 处理后的音频输入
    /// </summary>
    public class ProcessedAudioInput
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public byte[] AudioData { get; set; } = Array.Empty<byte>();
        public TimeSpan Duration { get; set; }
    }
}
