using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Services.DomainServices;
using myaiservice.Entities;
using myaiservice.Entities.ValueObjects;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Domain.Repositories;
using System.Text.Json;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// API代理控制器 - 处理AI模型请求
    /// </summary>
    [ApiController]
    [Route("v1")]
    [AllowAnonymous]
    public class ApiProxyController : AbpControllerBase
    {
        private readonly TokenManagementAppService _tokenManagementAppService;
        private readonly ChannelManagementAppService _channelManagementAppService;
        private readonly TokenDomainService _tokenDomainService;
        private readonly ChannelDomainService _channelDomainService;
        private readonly IRepository<RequestLog, Guid> _requestLogRepository;
        private readonly IRepository<Model, Guid> _modelRepository;
        private readonly IHttpClientFactory _httpClientFactory;

        public ApiProxyController(
            TokenManagementAppService tokenManagementAppService,
            ChannelManagementAppService channelManagementAppService,
            TokenDomainService tokenDomainService,
            ChannelDomainService channelDomainService,
            IRepository<RequestLog, Guid> requestLogRepository,
            IRepository<Model, Guid> modelRepository,
            IHttpClientFactory httpClientFactory)
        {
            _tokenManagementAppService = tokenManagementAppService;
            _channelManagementAppService = channelManagementAppService;
            _tokenDomainService = tokenDomainService;
            _channelDomainService = channelDomainService;
            _requestLogRepository = requestLogRepository;
            _modelRepository = modelRepository;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 聊天补全接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("chat/completions")]
        public async Task<IActionResult> ChatCompletionsAsync([FromBody] ApiRequestDto request)
        {
            return await ProcessApiRequestAsync(request, RequestType.ChatCompletion, "/v1/chat/completions");
        }

        /// <summary>
        /// 文本补全接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("completions")]
        public async Task<IActionResult> CompletionsAsync([FromBody] JsonElement request)
        {
            var apiRequest = JsonSerializer.Deserialize<ApiRequestDto>(request.GetRawText());
            return await ProcessApiRequestAsync(apiRequest!, RequestType.TextCompletion, "/v1/completions");
        }

        /// <summary>
        /// 文本嵌入接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("embeddings")]
        public async Task<IActionResult> EmbeddingsAsync([FromBody] JsonElement request)
        {
            var apiRequest = JsonSerializer.Deserialize<ApiRequestDto>(request.GetRawText());
            return await ProcessApiRequestAsync(apiRequest!, RequestType.Embedding, "/v1/embeddings");
        }

        /// <summary>
        /// 图像生成接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("images/generations")]
        public async Task<IActionResult> ImageGenerationsAsync([FromBody] JsonElement request)
        {
            var apiRequest = JsonSerializer.Deserialize<ApiRequestDto>(request.GetRawText());
            return await ProcessApiRequestAsync(apiRequest!, RequestType.ImageGeneration, "/v1/images/generations");
        }

        /// <summary>
        /// 语音识别接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("audio/transcriptions")]
        public async Task<IActionResult> AudioTranscriptionsAsync([FromForm] IFormFile file, [FromForm] string model)
        {
            // TODO: 实现音频文件处理
            return BadRequest("Audio transcription not implemented yet");
        }

        /// <summary>
        /// 语音合成接口 (OpenAI兼容)
        /// </summary>
        [HttpPost("audio/speech")]
        public async Task<IActionResult> AudioSpeechAsync([FromBody] JsonElement request)
        {
            var apiRequest = JsonSerializer.Deserialize<ApiRequestDto>(request.GetRawText());
            return await ProcessApiRequestAsync(apiRequest!, RequestType.TextToSpeech, "/v1/audio/speech");
        }

        /// <summary>
        /// 处理API请求的核心方法
        /// </summary>
        private async Task<IActionResult> ProcessApiRequestAsync(ApiRequestDto request, RequestType requestType, string endpoint)
        {
            var requestLog = new RequestLog(GuidGenerator.Create(), endpoint, "POST", requestType);
            var startTime = DateTime.UtcNow;
            
            try
            {
                // 1. 验证Token
                var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new { error = "Missing or invalid authorization header" });
                }

                var tokenValue = authHeader.Substring("Bearer ".Length).Trim();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
                
                var tokenValidation = await _tokenManagementAppService.ValidateTokenAsync(tokenValue, ipAddress, request.Model);
                if (!tokenValidation.IsValid)
                {
                    requestLog.SetResponseInfo(401, null, null, false, tokenValidation.ErrorMessage, "INVALID_TOKEN");
                    await _requestLogRepository.InsertAsync(requestLog);
                    return Unauthorized(new { error = tokenValidation.ErrorMessage });
                }

                requestLog.SetUserInfo(tokenValidation.UserId, tokenValidation.TokenId);
                requestLog.SetRequestInfo(ipAddress, HttpContext.Request.Headers.UserAgent, 
                    JsonSerializer.Serialize(HttpContext.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())),
                    JsonSerializer.Serialize(request));

                // 2. 获取模型信息
                var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == request.Model && m.IsEnabled);
                if (model == null)
                {
                    requestLog.SetResponseInfo(400, null, null, false, "Model not found or disabled", "MODEL_NOT_FOUND");
                    await _requestLogRepository.InsertAsync(requestLog);
                    return BadRequest(new { error = $"Model '{request.Model}' not found or disabled" });
                }

                requestLog.SetModelInfo(model.Id, model.Name);

                // 3. 选择最佳渠道
                var channel = await _channelManagementAppService.SelectBestChannelAsync(request.Model);
                if (channel == null)
                {
                    requestLog.SetResponseInfo(503, null, null, false, "No available channels", "NO_CHANNELS");
                    await _requestLogRepository.InsertAsync(requestLog);
                    return StatusCode(503, new { error = "No available channels for this model" });
                }

                requestLog.SetChannelInfo(channel.Id, channel.Name, channel.Type.ToString());

                // 4. 检查配额
                var estimatedCost = EstimateRequestCost(request, model);
                var (hasSufficientQuota, availableQuota) = await _tokenDomainService.CheckQuotaAsync(tokenValidation.TokenId!.Value, estimatedCost);
                
                if (!hasSufficientQuota)
                {
                    requestLog.SetResponseInfo(402, null, null, false, "Insufficient quota", "INSUFFICIENT_QUOTA");
                    await _requestLogRepository.InsertAsync(requestLog);
                    return StatusCode(402, new { error = "Insufficient quota", available = availableQuota, required = estimatedCost });
                }

                // 5. 转发请求到渠道
                var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {channel.ApiKey}");
                if (!string.IsNullOrEmpty(channel.OrganizationId))
                {
                    httpClient.DefaultRequestHeaders.Add("OpenAI-Organization", channel.OrganizationId);
                }

                var requestContent = new StringContent(JsonSerializer.Serialize(request), System.Text.Encoding.UTF8, "application/json");
                var channelUrl = $"{channel.BaseUrl.TrimEnd('/')}{endpoint}";
                
                var response = await httpClient.PostAsync(channelUrl, requestContent);
                var responseContent = await response.Content.ReadAsStringAsync();
                var responseHeaders = response.Headers.ToDictionary(h => h.Key, h => string.Join(",", h.Value));

                var endTime = DateTime.UtcNow;
                var responseTime = (endTime - startTime).TotalMilliseconds;

                // 6. 处理响应
                if (response.IsSuccessStatusCode)
                {
                    // 解析Token使用情况
                    var tokenUsage = ParseTokenUsage(responseContent);
                    var actualCost = model.CalculateCost(tokenUsage.InputTokens, tokenUsage.OutputTokens);

                    // 记录成功
                    requestLog.SetResponseInfo((int)response.StatusCode, JsonSerializer.Serialize(responseHeaders), responseContent, true);
                    requestLog.SetTokenUsage(tokenUsage.InputTokens, tokenUsage.OutputTokens);
                    requestLog.SetCost(actualCost);
                    requestLog.SetStreamingInfo(request.Stream ?? false);

                    // 消费配额
                    await _tokenDomainService.ConsumeQuotaAsync(tokenValidation.TokenId!.Value, actualCost, tokenUsage, ipAddress);

                    // 记录渠道使用成功
                    await _channelDomainService.RecordChannelSuccessAsync(channel.Id, responseTime);

                    // 记录模型使用
                    model.RecordUsage(tokenUsage.InputTokens, tokenUsage.OutputTokens, actualCost, responseTime);
                    await _modelRepository.UpdateAsync(model);

                    await _requestLogRepository.InsertAsync(requestLog);

                    // 返回响应
                    return Content(responseContent, "application/json");
                }
                else
                {
                    // 记录失败
                    requestLog.SetResponseInfo((int)response.StatusCode, JsonSerializer.Serialize(responseHeaders), responseContent, false, "Channel request failed");
                    await _channelDomainService.RecordChannelFailureAsync(channel.Id, $"HTTP {response.StatusCode}: {responseContent}");
                    
                    model.RecordFailedRequest();
                    await _modelRepository.UpdateAsync(model);

                    await _requestLogRepository.InsertAsync(requestLog);

                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                // 记录异常
                requestLog.SetResponseInfo(500, null, null, false, ex.Message, "INTERNAL_ERROR");
                await _requestLogRepository.InsertAsync(requestLog);

                return StatusCode(500, new { error = "Internal server error", message = ex.Message });
            }
        }

        /// <summary>
        /// 估算请求成本
        /// </summary>
        private decimal EstimateRequestCost(ApiRequestDto request, Model model)
        {
            // 简单估算：基于消息长度估算Token数量
            var estimatedInputTokens = request.Messages?.Sum(m => m.Content.Length / 4) ?? 100; // 粗略估算：4字符=1token
            var estimatedOutputTokens = request.MaxTokens ?? 100;
            
            return model.CalculateCost(estimatedInputTokens, estimatedOutputTokens);
        }

        /// <summary>
        /// 解析Token使用情况
        /// </summary>
        private TokenUsage ParseTokenUsage(string responseContent)
        {
            try
            {
                var jsonDoc = JsonDocument.Parse(responseContent);
                if (jsonDoc.RootElement.TryGetProperty("usage", out var usageElement))
                {
                    var promptTokens = usageElement.TryGetProperty("prompt_tokens", out var pt) ? pt.GetInt64() : 0;
                    var completionTokens = usageElement.TryGetProperty("completion_tokens", out var ct) ? ct.GetInt64() : 0;
                    
                    return new TokenUsage(promptTokens, completionTokens);
                }
            }
            catch
            {
                // 解析失败，返回默认值
            }

            return TokenUsage.Empty;
        }
    }
}
