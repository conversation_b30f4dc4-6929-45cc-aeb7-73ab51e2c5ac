﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;

namespace myaiservice.Services;

public interface IProxyServiceAppService
{
    /// <summary>
    /// 获取模型列表
    /// </summary>
    /// <returns>模型列表</returns>
    Task GetModels();
}

public class ProxyServiceAppService : myaiserviceAppService
{

    /// <summary>
    /// 获取模型列表
    /// </summary>
    /// <returns>模型列表</returns>
    [HttpGet("models")]
    public async Task GetModels()
    {
        await Task.CompletedTask;
    }

}
