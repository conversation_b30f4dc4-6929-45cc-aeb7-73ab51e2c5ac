using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Entities;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Domain.Repositories;
using Microsoft.AspNetCore.Authorization;
using System.Text.Json;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 管理后台控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "admin")]
    public class AdminController : AbpControllerBase
    {
        private readonly IRepository<UserProfile, Guid> _userProfileRepository;
        private readonly IRepository<Token, Guid> _tokenRepository;
        private readonly IRepository<Channel, Guid> _channelRepository;
        private readonly IRepository<Model, Guid> _modelRepository;
        private readonly IRepository<RequestLog, Guid> _requestLogRepository;
        private readonly IRepository<Quota, Guid> _quotaRepository;

        public AdminController(
            IRepository<UserProfile, Guid> userProfileRepository,
            IRepository<Token, Guid> tokenRepository,
            IRepository<Channel, Guid> channelRepository,
            IRepository<Model, Guid> modelRepository,
            IRepository<RequestLog, Guid> requestLogRepository,
            IRepository<Quota, Guid> quotaRepository)
        {
            _userProfileRepository = userProfileRepository;
            _tokenRepository = tokenRepository;
            _channelRepository = channelRepository;
            _modelRepository = modelRepository;
            _requestLogRepository = requestLogRepository;
            _quotaRepository = quotaRepository;
        }

        /// <summary>
        /// 获取系统统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<SystemStatisticsDto> GetSystemStatisticsAsync()
        {
            var totalUsers = await _userProfileRepository.CountAsync();
            var activeUsers = await _userProfileRepository.CountAsync(u => u.Status == UserStatus.Active);
            
            var totalTokens = await _tokenRepository.CountAsync();
            var activeTokens = await _tokenRepository.CountAsync(t => t.IsEnabled && !t.IsExpired());
            
            var totalChannels = await _channelRepository.CountAsync();
            var activeChannels = await _channelRepository.CountAsync(c => c.Status == ChannelStatus.Enabled);
            
            var totalModels = await _modelRepository.CountAsync();
            var activeModels = await _modelRepository.CountAsync(m => m.IsEnabled && !m.IsDeprecated);
            
            var totalRequests = await _requestLogRepository.CountAsync();
            var successfulRequests = await _requestLogRepository.CountAsync(r => r.IsSuccess);
            
            var totalTokensConsumed = (await _requestLogRepository.GetQueryableAsync())
                .Where(r => r.IsSuccess)
                .Sum(r => r.TotalTokens);
            
            var totalCost = (await _requestLogRepository.GetQueryableAsync())
                .Where(r => r.IsSuccess)
                .Sum(r => r.Cost);
            
            var averageResponseTime = totalRequests > 0 
                ? (await _requestLogRepository.GetQueryableAsync()).Average(r => r.ResponseTime)
                : 0;

            return new SystemStatisticsDto
            {
                TotalUsers = totalUsers,
                ActiveUsers = activeUsers,
                TotalTokens = totalTokens,
                ActiveTokens = activeTokens,
                TotalChannels = totalChannels,
                ActiveChannels = activeChannels,
                TotalModels = totalModels,
                ActiveModels = activeModels,
                TotalRequests = totalRequests,
                SuccessfulRequests = successfulRequests,
                OverallSuccessRate = totalRequests > 0 ? (double)successfulRequests / totalRequests * 100 : 0,
                TotalTokensConsumed = totalTokensConsumed,
                TotalCost = totalCost,
                TotalRevenue = totalCost, // 简化：假设成本等于收入
                AverageResponseTime = averageResponseTime,
                StatisticsTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 获取仪表板数据
        /// </summary>
        [HttpGet("dashboard")]
        public async Task<DashboardDto> GetDashboardAsync()
        {
            var systemStats = await GetSystemStatisticsAsync();
            
            // 获取热门模型
            var topModels = await GetTopModelsAsync(10);
            
            // 获取渠道统计
            var topChannels = await GetTopChannelsAsync(10);
            
            // 获取最近活跃用户
            var recentUsers = await GetRecentUsersAsync(10);
            
            // 获取最近请求
            var recentRequests = await GetRecentRequestsAsync(20);
            
            // 获取每日统计
            var dailyStats = await GetDailyStatisticsAsync(30);

            return new DashboardDto
            {
                SystemStatistics = systemStats,
                TopModels = topModels,
                TopChannels = topChannels,
                RecentUsers = recentUsers,
                RecentRequests = recentRequests,
                DailyStatistics = dailyStats
            };
        }

        /// <summary>
        /// 获取热门模型
        /// </summary>
        [HttpGet("top-models")]
        public async Task<List<ModelUsageDto>> GetTopModelsAsync(int count = 10)
        {
            var models = await _modelRepository.GetListAsync();
            
            return models
                .OrderByDescending(m => m.TotalRequests)
                .Take(count)
                .Select(m => new ModelUsageDto
                {
                    ModelId = m.Id,
                    ModelName = m.Name,
                    RequestCount = m.TotalRequests,
                    TokenCount = m.TotalTokens,
                    Cost = m.TotalCost,
                    SuccessRate = m.GetSuccessRate()
                })
                .ToList();
        }

        /// <summary>
        /// 获取渠道统计
        /// </summary>
        [HttpGet("top-channels")]
        public async Task<List<ChannelStatisticsDto>> GetTopChannelsAsync(int count = 10)
        {
            var channels = await _channelRepository.GetListAsync();
            
            return channels
                .OrderByDescending(c => c.TotalRequests)
                .Take(count)
                .Select(c => new ChannelStatisticsDto
                {
                    ChannelId = c.Id,
                    Name = c.Name,
                    Type = c.Type,
                    Status = c.Status,
                    TotalRequests = c.TotalRequests,
                    SuccessfulRequests = c.SuccessfulRequests,
                    SuccessRate = c.GetSuccessRate(),
                    AverageResponseTime = c.AverageResponseTime,
                    Balance = c.Balance,
                    FailureCount = c.FailureCount,
                    LastUsedTime = c.LastUsedTime,
                    LastCheckedTime = c.LastCheckedTime
                })
                .ToList();
        }

        /// <summary>
        /// 获取最近活跃用户
        /// </summary>
        [HttpGet("recent-users")]
        public async Task<List<UserActivityDto>> GetRecentUsersAsync(int count = 10)
        {
            var users = await _userProfileRepository.GetListAsync();
            
            return users
                .Where(u => u.LastLoginTime.HasValue)
                .OrderByDescending(u => u.LastLoginTime)
                .Take(count)
                .Select(u => new UserActivityDto
                {
                    UserId = u.UserId,
                    UserName = $"User_{u.UserId.ToString()[..8]}", // 简化显示
                    RequestCount = u.TotalRequests,
                    TokenCount = u.TotalTokens,
                    Cost = u.TotalCost,
                    LastActivityTime = u.LastLoginTime
                })
                .ToList();
        }

        /// <summary>
        /// 获取最近请求
        /// </summary>
        [HttpGet("recent-requests")]
        public async Task<List<RequestLogDto>> GetRecentRequestsAsync(int count = 20)
        {
            var requests = await _requestLogRepository.GetListAsync();
            
            return requests
                .OrderByDescending(r => r.RequestStartTime)
                .Take(count)
                .Select(r => ObjectMapper.Map<RequestLog, RequestLogDto>(r))
                .ToList();
        }

        /// <summary>
        /// 获取每日统计
        /// </summary>
        [HttpGet("daily-statistics")]
        public async Task<List<DailyStatisticsDto>> GetDailyStatisticsAsync(int days = 30)
        {
            var startDate = DateTime.UtcNow.Date.AddDays(-days);
            var requests = await _requestLogRepository.GetListAsync(r => r.RequestStartTime >= startDate);
            
            return requests
                .GroupBy(r => r.RequestStartTime.Date)
                .Select(g => new DailyStatisticsDto
                {
                    Date = g.Key,
                    RequestCount = g.Count(),
                    TokenCount = g.Sum(r => r.TotalTokens),
                    Cost = g.Sum(r => r.Cost),
                    UserCount = g.Select(r => r.UserId).Distinct().Count(),
                    SuccessRate = g.Count() > 0 ? (double)g.Count(r => r.IsSuccess) / g.Count() * 100 : 0
                })
                .OrderBy(d => d.Date)
                .ToList();
        }

        /// <summary>
        /// 获取系统配置
        /// </summary>
        [HttpGet("config")]
        public async Task<IActionResult> GetSystemConfigAsync()
        {
            // TODO: 实现系统配置获取
            return Ok(new { message = "System config endpoint" });
        }

        /// <summary>
        /// 更新系统配置
        /// </summary>
        [HttpPut("config")]
        public async Task<IActionResult> UpdateSystemConfigAsync([FromBody] JsonElement config)
        {
            // TODO: 实现系统配置更新
            return Ok(new { message = "System config updated" });
        }

        /// <summary>
        /// 系统健康检查
        /// </summary>
        [HttpGet("health")]
        [AllowAnonymous]
        public async Task<IActionResult> HealthCheckAsync()
        {
            try
            {
                // 检查数据库连接
                var userCount = await _userProfileRepository.CountAsync();
                
                return Ok(new 
                { 
                    status = "healthy", 
                    timestamp = DateTime.UtcNow,
                    database = "connected",
                    userCount = userCount
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    status = "unhealthy", 
                    timestamp = DateTime.UtcNow,
                    error = ex.Message
                });
            }
        }
    }
}
