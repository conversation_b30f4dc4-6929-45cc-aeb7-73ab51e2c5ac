using myaiservice.Entities;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 渠道领域服务
    /// </summary>
    public class ChannelDomainService : DomainService
    {
        private readonly IRepository<Channel, Guid> _channelRepository;
        private readonly IRepository<Model, Guid> _modelRepository;

        public ChannelDomainService(
            IRepository<Channel, Guid> channelRepository,
            IRepository<Model, Guid> modelRepository)
        {
            _channelRepository = channelRepository;
            _modelRepository = modelRepository;
        }

        /// <summary>
        /// 创建新渠道
        /// </summary>
        public async Task<Channel> CreateChannelAsync(string name, ChannelType type, string baseUrl, string apiKey)
        {
            // 检查渠道名称是否已存在
            var existingChannel = await _channelRepository.FirstOrDefaultAsync(c => c.Name == name);
            if (existingChannel != null)
                throw new InvalidOperationException($"Channel with name '{name}' already exists");

            var channel = new Channel(GuidGenerator.Create(), name, type, baseUrl, apiKey);
            return channel;
        }

        /// <summary>
        /// 获取可用的渠道列表
        /// </summary>
        public async Task<List<Channel>> GetAvailableChannelsAsync(string? modelName = null, string? group = null)
        {
            var query = await _channelRepository.GetQueryableAsync();
            
            // 筛选可用渠道
            query = query.Where(c => c.Status == ChannelStatus.Enabled && c.Balance > 0);
            
            // 按组筛选
            if (!string.IsNullOrEmpty(group))
            {
                query = query.Where(c => c.Group == group);
            }
            
            // 按模型筛选
            if (!string.IsNullOrEmpty(modelName))
            {
                query = query.Where(c => c.SupportedModels == null || c.SupportedModels.Contains(modelName));
            }
            
            // 按优先级和权重排序
            query = query.OrderBy(c => c.Priority).ThenByDescending(c => c.Weight);
            
            return await AsyncExecuter.ToListAsync(query);
        }

        /// <summary>
        /// 选择最佳渠道 (负载均衡)
        /// </summary>
        public async Task<Channel?> SelectBestChannelAsync(string? modelName = null, string? group = null)
        {
            var availableChannels = await GetAvailableChannelsAsync(modelName, group);
            
            if (!availableChannels.Any())
                return null;

            // 加权随机选择
            return SelectChannelByWeight(availableChannels);
        }

        /// <summary>
        /// 按权重选择渠道
        /// </summary>
        private Channel SelectChannelByWeight(List<Channel> channels)
        {
            var totalWeight = channels.Sum(c => c.Weight);
            var random = new Random();
            var randomValue = random.Next(1, totalWeight + 1);
            
            var currentWeight = 0;
            foreach (var channel in channels)
            {
                currentWeight += channel.Weight;
                if (randomValue <= currentWeight)
                    return channel;
            }
            
            return channels.First(); // 备用选择
        }

        /// <summary>
        /// 记录渠道使用成功
        /// </summary>
        public async Task RecordChannelSuccessAsync(Guid channelId, double responseTime)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.RecordSuccessfulRequest(responseTime);
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 记录渠道使用失败
        /// </summary>
        public async Task RecordChannelFailureAsync(Guid channelId, string? errorMessage = null)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.RecordFailedRequest(errorMessage);
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 执行渠道健康检查
        /// </summary>
        public async Task<ChannelHealthStatus> PerformHealthCheckAsync(Guid channelId)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            try
            {
                // TODO: 实现实际的健康检查逻辑
                // 这里应该调用渠道的API进行健康检查
                var isHealthy = await CheckChannelHealthAsync(channel);
                
                channel.RecordHealthCheck(isHealthy);
                await _channelRepository.UpdateAsync(channel);
                
                return new ChannelHealthStatus
                {
                    ChannelId = channelId,
                    IsHealthy = isHealthy,
                    CheckTime = DateTime.UtcNow,
                    ResponseTime = channel.AverageResponseTime,
                    ErrorMessage = isHealthy ? null : channel.LastError
                };
            }
            catch (Exception ex)
            {
                channel.RecordHealthCheck(false, ex.Message);
                await _channelRepository.UpdateAsync(channel);
                
                return new ChannelHealthStatus
                {
                    ChannelId = channelId,
                    IsHealthy = false,
                    CheckTime = DateTime.UtcNow,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 实际的健康检查实现
        /// </summary>
        private async Task<bool> CheckChannelHealthAsync(Channel channel)
        {
            // TODO: 根据渠道类型实现具体的健康检查
            // 例如：发送简单的API请求来验证渠道是否可用
            await Task.Delay(100); // 模拟网络请求
            return true; // 临时返回true
        }

        /// <summary>
        /// 批量健康检查
        /// </summary>
        public async Task<List<ChannelHealthStatus>> PerformBatchHealthCheckAsync()
        {
            var channels = await _channelRepository.GetListAsync(c => c.Status == ChannelStatus.Enabled);
            var healthStatuses = new List<ChannelHealthStatus>();
            
            foreach (var channel in channels)
            {
                var healthStatus = await PerformHealthCheckAsync(channel.Id);
                healthStatuses.Add(healthStatus);
            }
            
            return healthStatuses;
        }

        /// <summary>
        /// 更新渠道余额
        /// </summary>
        public async Task UpdateChannelBalanceAsync(Guid channelId, decimal newBalance)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.UpdateBalance(newBalance);
            
            // 如果余额不足，更新状态
            if (newBalance <= 0 && channel.Status == ChannelStatus.Enabled)
            {
                channel.Status = ChannelStatus.InsufficientBalance;
            }
            else if (newBalance > 0 && channel.Status == ChannelStatus.InsufficientBalance)
            {
                channel.Status = ChannelStatus.Enabled;
            }
            
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 启用渠道
        /// </summary>
        public async Task EnableChannelAsync(Guid channelId)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.Enable();
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 禁用渠道
        /// </summary>
        public async Task DisableChannelAsync(Guid channelId)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.Disable();
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 重置渠道失败计数
        /// </summary>
        public async Task ResetChannelFailureCountAsync(Guid channelId)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            channel.ResetFailureCount();
            await _channelRepository.UpdateAsync(channel);
        }

        /// <summary>
        /// 获取渠道统计信息
        /// </summary>
        public async Task<ChannelStatistics> GetChannelStatisticsAsync(Guid channelId)
        {
            var channel = await _channelRepository.GetAsync(channelId);
            
            return new ChannelStatistics
            {
                ChannelId = channelId,
                Name = channel.Name,
                Type = channel.Type,
                Status = channel.Status,
                TotalRequests = channel.TotalRequests,
                SuccessfulRequests = channel.SuccessfulRequests,
                SuccessRate = channel.GetSuccessRate(),
                AverageResponseTime = channel.AverageResponseTime,
                Balance = channel.Balance,
                FailureCount = channel.FailureCount,
                LastUsedTime = channel.LastUsedTime,
                LastCheckedTime = channel.LastCheckedTime
            };
        }

        /// <summary>
        /// 自动禁用失败过多的渠道
        /// </summary>
        public async Task AutoDisableFailedChannelsAsync()
        {
            var failedChannels = await _channelRepository.GetListAsync(c => 
                c.Status == ChannelStatus.Enabled && 
                c.FailureCount >= c.MaxFailures);

            foreach (var channel in failedChannels)
            {
                channel.Status = ChannelStatus.DisabledByFailure;
            }

            await _channelRepository.UpdateManyAsync(failedChannels);
        }
    }

    /// <summary>
    /// 渠道健康状态
    /// </summary>
    public class ChannelHealthStatus
    {
        public Guid ChannelId { get; set; }
        public bool IsHealthy { get; set; }
        public DateTime CheckTime { get; set; }
        public double ResponseTime { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 渠道统计信息
    /// </summary>
    public class ChannelStatistics
    {
        public Guid ChannelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public ChannelType Type { get; set; }
        public ChannelStatus Status { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public double AverageResponseTime { get; set; }
        public decimal Balance { get; set; }
        public int FailureCount { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public DateTime? LastCheckedTime { get; set; }
    }
}
