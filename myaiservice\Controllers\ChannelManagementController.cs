using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 渠道管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "admin")]
    public class ChannelManagementController : AbpControllerBase
    {
        private readonly ChannelManagementAppService _channelManagementAppService;

        public ChannelManagementController(ChannelManagementAppService channelManagementAppService)
        {
            _channelManagementAppService = channelManagementAppService;
        }

        /// <summary>
        /// 创建渠道
        /// </summary>
        [HttpPost]
        public async Task<ChannelDto> CreateChannelAsync([FromBody] CreateChannelDto input)
        {
            return await _channelManagementAppService.CreateChannelAsync(input);
        }

        /// <summary>
        /// 获取所有渠道
        /// </summary>
        [HttpGet]
        public async Task<List<ChannelDto>> GetChannelsAsync()
        {
            return await _channelManagementAppService.GetChannelsAsync();
        }

        /// <summary>
        /// 获取渠道详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ChannelDto> GetChannelAsync(Guid id)
        {
            return await _channelManagementAppService.GetChannelAsync(id);
        }

        /// <summary>
        /// 更新渠道
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ChannelDto> UpdateChannelAsync(Guid id, [FromBody] UpdateChannelDto input)
        {
            return await _channelManagementAppService.UpdateChannelAsync(id, input);
        }

        /// <summary>
        /// 删除渠道
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteChannelAsync(Guid id)
        {
            await _channelManagementAppService.DeleteChannelAsync(id);
            return Ok();
        }

        /// <summary>
        /// 启用渠道
        /// </summary>
        [HttpPost("{id}/enable")]
        public async Task<IActionResult> EnableChannelAsync(Guid id)
        {
            await _channelManagementAppService.EnableChannelAsync(id);
            return Ok();
        }

        /// <summary>
        /// 禁用渠道
        /// </summary>
        [HttpPost("{id}/disable")]
        public async Task<IActionResult> DisableChannelAsync(Guid id)
        {
            await _channelManagementAppService.DisableChannelAsync(id);
            return Ok();
        }

        /// <summary>
        /// 更新渠道余额
        /// </summary>
        [HttpPut("{id}/balance")]
        public async Task<IActionResult> UpdateChannelBalanceAsync(Guid id, [FromBody] UpdateBalanceRequest request)
        {
            await _channelManagementAppService.UpdateChannelBalanceAsync(id, request.Balance);
            return Ok();
        }

        /// <summary>
        /// 执行渠道健康检查
        /// </summary>
        [HttpPost("{id}/health-check")]
        public async Task<ChannelHealthStatusDto> PerformHealthCheckAsync(Guid id)
        {
            return await _channelManagementAppService.PerformHealthCheckAsync(id);
        }

        /// <summary>
        /// 批量健康检查
        /// </summary>
        [HttpPost("health-check")]
        public async Task<List<ChannelHealthStatusDto>> PerformBatchHealthCheckAsync()
        {
            return await _channelManagementAppService.PerformBatchHealthCheckAsync();
        }

        /// <summary>
        /// 重置渠道失败计数
        /// </summary>
        [HttpPost("{id}/reset-failures")]
        public async Task<IActionResult> ResetChannelFailureCountAsync(Guid id)
        {
            await _channelManagementAppService.ResetChannelFailureCountAsync(id);
            return Ok();
        }

        /// <summary>
        /// 获取渠道统计信息
        /// </summary>
        [HttpGet("{id}/statistics")]
        public async Task<ChannelStatisticsDto> GetChannelStatisticsAsync(Guid id)
        {
            return await _channelManagementAppService.GetChannelStatisticsAsync(id);
        }

        /// <summary>
        /// 获取可用渠道列表 (内部API)
        /// </summary>
        [HttpGet("available")]
        [AllowAnonymous]
        public async Task<List<ChannelDto>> GetAvailableChannelsAsync([FromQuery] string? modelName = null, [FromQuery] string? group = null)
        {
            return await _channelManagementAppService.GetAvailableChannelsAsync(modelName, group);
        }

        /// <summary>
        /// 选择最佳渠道 (内部API)
        /// </summary>
        [HttpPost("select-best")]
        [AllowAnonymous]
        public async Task<ChannelDto?> SelectBestChannelAsync([FromBody] SelectChannelRequest request)
        {
            return await _channelManagementAppService.SelectBestChannelAsync(request.ModelName, request.Group);
        }
    }

    // Request DTOs
    public class UpdateBalanceRequest
    {
        public decimal Balance { get; set; }
    }

    public class SelectChannelRequest
    {
        public string? ModelName { get; set; }
        public string? Group { get; set; }
    }
}
