# New API 项目功能分析文档

## 项目概述

New API 是一个基于 Go 语言开发的新一代大模型网关与AI资产管理系统，在 [One API](https://github.com/songquanpeng/one-api) 基础上进行二次开发。该项目提供了统一的API接口来管理和访问多种AI服务提供商，同时具备完整的用户管理、计费、监控等企业级功能。

## 技术栈

### 后端技术栈
- **语言**: Go 1.23.4
- **Web框架**: Gin
- **数据库**: 
  - SQLite (默认)
  - MySQL 5.7.8+
  - PostgreSQL 9.6+
- **ORM**: GORM
- **缓存**: Redis
- **认证**: JWT
- **WebSocket**: Gorilla WebSocket
- **容器化**: Docker

### 前端技术栈
- **框架**: React 18
- **UI库**: Semi Design
- **构建工具**: Vite
- **状态管理**: React Hooks
- **国际化**: i18next
- **图表**: VChart
- **样式**: Tailwind CSS

## 核心功能模块

### 1. 用户管理系统

#### 1.1 用户认证
- **注册登录**: 支持邮箱注册、密码登录
- **第三方登录**: 
  - GitHub OAuth
  - WeChat (微信)
  - Telegram
  - LinuxDO
  - OIDC (OpenID Connect)
- **多因素认证**: 邮箱验证、Turnstile验证码
- **密码管理**: 密码重置、密码强度验证

#### 1.2 用户权限管理
- **角色系统**: 
  - 超级管理员 (Root)
  - 管理员 (Admin)
  - 普通用户 (User)
- **权限控制**: 基于角色的访问控制(RBAC)
- **用户组**: 支持用户分组管理
- **IP限制**: 支持IP白名单限制

#### 1.3 用户资料管理
- 个人信息编辑
- 邀请码系统
- 用户设置自定义

### 2. 令牌(Token)管理系统

#### 2.1 API密钥管理
- **令牌生成**: 自动生成48位安全密钥
- **令牌类型**: 
  - 系统管理令牌
  - API访问令牌
- **生命周期管理**: 创建、启用、禁用、删除
- **访问统计**: 使用次数、最后访问时间

#### 2.2 令牌权限控制
- **配额限制**: 单个令牌的使用配额
- **模型限制**: 限制令牌可访问的模型
- **IP限制**: 令牌的IP访问限制
- **分组限制**: 基于用户组的访问控制

### 3. 渠道管理系统

#### 3.1 渠道配置
- **多渠道支持**: 支持30+AI服务提供商
- **渠道类型**: 
  - OpenAI及兼容接口
  - Claude (Anthropic)
  - Gemini (Google)
  - Azure OpenAI
  - 国产大模型（百度、讯飞、智谱等）
- **自定义渠道**: 支持自定义API地址
- **渠道分组**: 支持渠道标签分组

#### 3.2 渠道管理功能
- **状态监控**: 实时检测渠道可用性
- **负载均衡**: 
  - 加权随机分配
  - 优先级设置
  - 故障自动切换
- **余额管理**: 自动查询和更新渠道余额
- **模型映射**: 支持模型名称映射
- **参数覆盖**: 支持请求参数覆盖

#### 3.3 渠道测试与维护
- **健康检查**: 定期自动测试渠道可用性
- **响应时间统计**: 记录渠道响应性能
- **错误日志**: 详细的错误信息记录
- **自动禁用**: 支持故障渠道自动禁用

### 4. AI模型支持

#### 4.1 文本生成模型
- **OpenAI系列**: GPT-3.5, GPT-4, GPT-4 Turbo, GPT-4o, o1系列
- **Claude系列**: Claude-3 Haiku/Sonnet/Opus, Claude-3.5
- **Gemini系列**: Gemini Pro, Gemini Ultra
- **国产模型**: 
  - 百度文心一言
  - 讯飞星火
  - 智谱GLM系列
  - 月之暗面Moonshot
  - DeepSeek系列
  - 阿里通义千问
  - 腾讯混元
  - 字节豆包

#### 4.2 多模态模型
- **视觉理解**: GPT-4V, Gemini Vision, Claude-3等
- **图像生成**: DALL-E, Midjourney, Stable Diffusion
- **音频处理**: Whisper (语音转文字), TTS (文字转语音)
- **音乐生成**: Suno API集成

#### 4.3 专用模型
- **Embedding模型**: 文本向量化
- **Rerank模型**: Cohere, Jina AI
- **代码生成**: GitHub Copilot兼容接口

### 5. 计费与配额系统

#### 5.1 计费模式
- **按Token计费**: 基于输入/输出Token数量
- **按次数计费**: 固定每次调用费用
- **缓存计费**: 支持缓存命中时按比例计费
- **图片Token统计**: 支持多模态内容计费

#### 5.2 配额管理
- **用户配额**: 用户总配额限制
- **令牌配额**: 单个令牌配额限制
- **实时扣费**: 请求完成后实时扣除配额
- **配额预警**: 配额不足时自动通知

#### 5.3 充值系统
- **在线充值**: 集成易支付等支付接口
- **充值码**: 支持充值卡系统
- **邀请返利**: 邀请用户获得返利配额
- **管理员充值**: 管理员为用户充值

### 6. 请求中继系统

#### 6.1 API兼容性
- **OpenAI API兼容**: 完整兼容OpenAI API格式
- **Claude Messages格式**: 支持Anthropic原生格式
- **自定义格式**: 支持各厂商原生API格式
- **参数转换**: 自动进行参数格式转换

#### 6.2 请求处理
- **流式响应**: 支持Server-Sent Events (SSE)
- **WebSocket**: 支持实时对话(Realtime API)
- **文件上传**: 支持图片、音频等文件上传
- **请求压缩**: 支持请求内容压缩

#### 6.3 错误处理与重试
- **自动重试**: 请求失败时自动重试其他渠道
- **错误映射**: 统一错误码映射
- **熔断机制**: 防止雪崩效应
- **超时控制**: 可配置的请求超时时间

### 7. 日志与监控系统

#### 7.1 请求日志
- **详细记录**: 记录所有API请求详情
- **用户行为**: 记录用户操作行为
- **性能指标**: 请求响应时间、Token消耗等
- **错误日志**: 详细的错误信息和堆栈

#### 7.2 统计分析
- **使用统计**: 按用户、模型、时间等维度统计
- **成本分析**: 各渠道成本分析
- **性能分析**: 渠道性能对比分析
- **趋势分析**: 使用趋势和预测

#### 7.3 数据可视化
- **控制台看板**: 实时数据展示
- **图表分析**: 多维度图表展示
- **导出功能**: 支持数据导出
- **报表生成**: 定期报表生成

### 8. 管理后台系统

#### 8.1 系统设置
- **全局配置**: 系统级别配置管理
- **功能开关**: 各功能模块开关控制
- **安全设置**: 安全策略配置
- **通知设置**: 系统通知配置

#### 8.2 用户管理
- **用户列表**: 查看所有用户信息
- **用户搜索**: 按条件搜索用户
- **用户操作**: 启用/禁用/删除用户
- **配额管理**: 为用户分配配额

#### 8.3 渠道管理
- **渠道配置**: 添加、编辑、删除渠道
- **批量操作**: 批量启用/禁用渠道
- **模型管理**: 管理渠道支持的模型
- **测试工具**: 渠道连通性测试

### 9. 高级功能

#### 9.1 缓存系统
- **Redis缓存**: 支持Redis作为缓存后端
- **内存缓存**: 支持内存缓存
- **缓存策略**: 可配置的缓存过期策略
- **缓存同步**: 多实例间缓存同步

#### 9.2 限流与安全
- **API限流**: 全局和用户级别限流
- **模型限流**: 针对特定模型的限流
- **IP限制**: 基于IP的访问控制
- **请求校验**: 请求参数合法性校验

#### 9.3 多实例部署
- **负载均衡**: 支持多实例负载均衡
- **会话同步**: 多实例间会话同步
- **数据一致性**: 确保多实例数据一致性
- **故障转移**: 实例故障时自动转移

## 支持的AI服务提供商

### 国际服务商
- OpenAI (GPT系列)
- Anthropic (Claude系列)
- Google (Gemini系列)
- Microsoft Azure OpenAI
- Cohere (Embedding, Rerank)
- Perplexity AI
- Mistral AI
- Together AI
- OpenRouter
- AWS Bedrock

### 国内服务商
- 百度智能云 (文心一言)
- 讯飞开放平台 (星火大模型)
- 智谱AI (GLM系列)
- 月之暗面 (Moonshot)
- DeepSeek
- 阿里云 (通义千问)
- 腾讯云 (混元大模型)
- 字节跳动 (豆包)
- 零一万物 (Yi系列)
- 360AI (360智脑)
- MiniMax
- SiliconFlow

### 特殊服务
- Midjourney (图像生成)
- Suno (音乐生成)
- Dify (工作流)
- Ollama (本地部署)

## 部署架构

### 单机部署
- Docker容器部署
- 二进制文件部署
- 宝塔面板一键部署

### 分布式部署
- 多实例负载均衡
- Redis集群
- 数据库读写分离
- CDN加速

### 云原生部署
- Kubernetes支持
- Helm Chart
- 自动扩缩容
- 服务网格集成

## 环境变量配置

### 核心配置
- `PORT`: 服务端口 (默认3000)
- `SESSION_SECRET`: 会话密钥
- `SQL_DSN`: 数据库连接字符串
- `REDIS_CONN_STRING`: Redis连接字符串

### 功能配置
- `GENERATE_DEFAULT_TOKEN`: 是否生成默认Token
- `STREAMING_TIMEOUT`: 流式响应超时时间
- `FORCE_STREAM_OPTION`: 是否强制返回usage信息
- `GET_MEDIA_TOKEN`: 是否统计图片Token
- `UPDATE_TASK`: 是否更新异步任务
- `CRYPTO_SECRET`: 数据加密密钥

### 第三方集成
- `GITHUB_CLIENT_ID/SECRET`: GitHub OAuth配置
- `WECHAT_CLIENT_ID/SECRET`: 微信登录配置
- `TELEGRAM_BOT_TOKEN`: Telegram机器人配置
- `TURNSTILE_SECRET_KEY`: Cloudflare验证配置

## API接口文档

### 认证接口
- POST `/api/user/register` - 用户注册
- POST `/api/user/login` - 用户登录
- GET `/api/user/logout` - 用户登出

### 管理接口
- GET `/api/user/` - 获取用户列表
- GET `/api/channel/` - 获取渠道列表
- GET `/api/token/` - 获取令牌列表
- GET `/api/log/` - 获取日志列表

### 中继接口
- POST `/v1/chat/completions` - 聊天补全
- POST `/v1/completions` - 文本补全
- POST `/v1/embeddings` - 文本向量化
- POST `/v1/images/generations` - 图像生成
- POST `/v1/audio/transcriptions` - 语音识别
- POST `/v1/rerank` - 文档重排序

## 安全特性

### 数据安全
- 密码加密存储
- 敏感数据加密
- API密钥安全存储
- 数据传输加密

### 访问控制
- JWT令牌认证
- IP白名单限制
- 角色权限控制
- API调用限流

### 审计追踪
- 完整操作日志
- 异常行为检测
- 安全事件告警
- 合规性报告

## 性能特性

### 高性能
- 连接池管理
- 异步处理
- 缓存机制
- 请求去重

### 高可用
- 多渠道冗余
- 自动故障转移
- 健康检查
- 优雅重启

### 可扩展
- 水平扩展
- 微服务架构
- 插件机制
- 配置热更新

## 监控与运维

### 系统监控
- 服务状态监控
- 资源使用监控
- 性能指标监控
- 错误率监控

### 业务监控
- API调用量监控
- 成本消耗监控
- 用户活跃度监控
- 渠道可用性监控

### 告警通知
- 邮件通知
- 微信通知
- Webhook通知
- 系统日志

## 扩展能力

### 插件系统
- 自定义渠道插件
- 认证插件
- 计费插件
- 监控插件

### 开放API
- RESTful API
- GraphQL支持
- Webhook回调
- SDK支持

### 第三方集成
- 支付系统集成
- 监控系统集成
- 日志系统集成
- 通知系统集成

## 总结

New API 是一个功能完整、架构先进的AI模型网关系统，具备以下核心优势：

1. **全面的AI模型支持**: 支持30+主流AI服务提供商
2. **企业级功能**: 完整的用户管理、计费、监控体系
3. **高可用架构**: 支持多实例部署、故障自动转移
4. **灵活的配置**: 丰富的配置选项和自定义能力
5. **开放的生态**: 良好的扩展性和第三方集成能力
6. **现代化技术栈**: 基于Go和React的现代技术架构
7. **完善的文档**: 详细的部署和使用文档

该系统适用于需要统一管理多个AI服务的企业和开发者，提供了从简单的API代理到复杂的AI资产管理的完整解决方案。