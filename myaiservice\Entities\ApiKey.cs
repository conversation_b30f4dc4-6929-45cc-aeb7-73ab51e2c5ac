﻿using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities;


/// <summary>
/// API密钥实体
/// </summary>
public class ApiKey : AuditedAggregateRoot<Guid>
{

    /// <summary>
    /// 密钥值
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Value { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 失败次数
    /// </summary>
    public int FailureCount { get; set; } = 0;

    /// <summary>
    /// 上次使用时间
    /// </summary>
    public DateTime? LastUsedTime { get; set; }

    /// <summary>
    /// 上次检查时间
    /// </summary>
    public DateTime? LastCheckedTime { get; set; }

    /// <summary>
    /// 总调用次数
    /// </summary>
    public int TotalCalls { get; set; } = 0;

    /// <summary>
    /// 创建一个新的API密钥实体
    /// </summary>
    public ApiKey()
    {
    }

    /// <summary>
    /// 使用指定的密钥值创建一个新的API密钥实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <param name="value">密钥值</param>
    public ApiKey(Guid id, string value)
    {
        Id = id;
        Value = value;
        IsEnabled = true;
        FailureCount = 0;
        TotalCalls = 0;
    }

    /// <summary>
    /// 增加失败次数
    /// </summary>
    public void IncrementFailureCount()
    {
        FailureCount++;
    }

    /// <summary>
    /// 重置失败次数
    /// </summary>
    public void ResetFailureCount()
    {
        FailureCount = 0;
    }

    /// <summary>
    /// 记录使用
    /// </summary>
    public void RecordUsage()
    {
        LastUsedTime = DateTime.UtcNow;
        TotalCalls++;
    }

    /// <summary>
    /// 记录检查
    /// </summary>
    public void RecordCheck()
    {
        LastCheckedTime = DateTime.UtcNow;
    }
}

