using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Services.DomainServices;
using myaiservice.Entities;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 配额管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class QuotaManagementController : AbpControllerBase
    {
        private readonly QuotaManagementAppService _quotaManagementAppService;

        public QuotaManagementController(QuotaManagementAppService quotaManagementAppService)
        {
            _quotaManagementAppService = quotaManagementAppService;
        }

        /// <summary>
        /// 获取当前用户配额信息
        /// </summary>
        [HttpGet("my-quota")]
        public async Task<UserQuotaStatistics> GetMyQuotaAsync()
        {
            return await _quotaManagementAppService.GetMyQuotaAsync();
        }

        /// <summary>
        /// 获取指定用户配额信息
        /// </summary>
        [HttpGet("users/{userId}/quota")]
        [Authorize(Roles = "admin")]
        public async Task<UserQuotaStatistics> GetUserQuotaAsync(Guid userId)
        {
            return await _quotaManagementAppService.GetUserQuotaAsync(userId);
        }

        /// <summary>
        /// 获取当前用户配额交易记录
        /// </summary>
        [HttpGet("my-transactions")]
        public async Task<List<QuotaTransactionDto>> GetMyQuotaTransactionsAsync([FromQuery] int maxResultCount = 50, [FromQuery] int skipCount = 0)
        {
            return await _quotaManagementAppService.GetMyQuotaTransactionsAsync(maxResultCount, skipCount);
        }

        /// <summary>
        /// 获取指定用户配额交易记录
        /// </summary>
        [HttpGet("users/{userId}/transactions")]
        [Authorize(Roles = "admin")]
        public async Task<List<QuotaTransactionDto>> GetUserQuotaTransactionsAsync(Guid userId, [FromQuery] int maxResultCount = 50, [FromQuery] int skipCount = 0)
        {
            return await _quotaManagementAppService.GetUserQuotaTransactionsAsync(userId, maxResultCount, skipCount);
        }

        /// <summary>
        /// 为用户充值配额
        /// </summary>
        [HttpPost("recharge")]
        [Authorize(Roles = "admin")]
        public async Task<QuotaDto> RechargeUserQuotaAsync([FromBody] RechargeQuotaDto input)
        {
            return await _quotaManagementAppService.RechargeUserQuotaAsync(input);
        }

        /// <summary>
        /// 创建配额
        /// </summary>
        [HttpPost("create")]
        [Authorize(Roles = "admin")]
        public async Task<QuotaDto> CreateQuotaAsync([FromBody] CreateQuotaDto input)
        {
            return await _quotaManagementAppService.CreateQuotaAsync(input);
        }

        /// <summary>
        /// 管理员调整配额
        /// </summary>
        [HttpPut("adjust")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> AdminAdjustQuotaAsync([FromBody] AdminAdjustQuotaDto input)
        {
            await _quotaManagementAppService.AdminAdjustQuotaAsync(input);
            return Ok();
        }

        /// <summary>
        /// 退款配额
        /// </summary>
        [HttpPost("refund")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> RefundQuotaAsync([FromBody] RefundQuotaDto input)
        {
            var result = await _quotaManagementAppService.RefundQuotaAsync(input);
            if (result)
            {
                return Ok(new { success = true, message = "Refund processed successfully" });
            }
            return BadRequest(new { success = false, message = "Refund processing failed" });
        }

        /// <summary>
        /// 获取当前用户计费统计
        /// </summary>
        [HttpGet("my-billing-statistics")]
        public async Task<UserBillingStatistics> GetMyBillingStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _quotaManagementAppService.GetMyBillingStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取指定用户计费统计
        /// </summary>
        [HttpGet("users/{userId}/billing-statistics")]
        [Authorize(Roles = "admin")]
        public async Task<UserBillingStatistics> GetUserBillingStatisticsAsync(Guid userId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _quotaManagementAppService.GetUserBillingStatisticsAsync(userId, startDate, endDate);
        }

        /// <summary>
        /// 获取模型计费统计
        /// </summary>
        [HttpGet("model-billing-statistics")]
        [Authorize(Roles = "admin")]
        public async Task<List<ModelBillingStatistics>> GetModelBillingStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _quotaManagementAppService.GetModelBillingStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 预估请求费用
        /// </summary>
        [HttpPost("estimate-cost")]
        public async Task<IActionResult> EstimateRequestCostAsync([FromBody] EstimateCostRequest request)
        {
            var cost = await _quotaManagementAppService.EstimateRequestCostAsync(request.ModelName, request.EstimatedInputTokens, request.EstimatedOutputTokens);
            return Ok(new { model = request.ModelName, estimated_cost = cost, currency = "USD" });
        }

        /// <summary>
        /// 检查配额是否足够
        /// </summary>
        [HttpPost("check-quota")]
        public async Task<QuotaCheckResult> CheckQuotaAsync([FromBody] CheckQuotaRequest request)
        {
            return await _quotaManagementAppService.CheckQuotaAsync(request.ModelName, request.EstimatedInputTokens, request.EstimatedOutputTokens);
        }

        /// <summary>
        /// 获取配额使用趋势
        /// </summary>
        [HttpGet("my-usage-trend")]
        public async Task<List<QuotaUsageTrend>> GetMyQuotaUsageTrendAsync([FromQuery] int days = 30)
        {
            return await _quotaManagementAppService.GetMyQuotaUsageTrendAsync(days);
        }

        /// <summary>
        /// 获取配额预警信息
        /// </summary>
        [HttpGet("my-alerts")]
        public async Task<List<QuotaAlert>> GetQuotaAlertsAsync()
        {
            return await _quotaManagementAppService.GetQuotaAlertsAsync();
        }

        /// <summary>
        /// 获取配额类型列表
        /// </summary>
        [HttpGet("quota-types")]
        public IActionResult GetQuotaTypes()
        {
            var quotaTypes = Enum.GetValues<QuotaType>()
                .Select(qt => new { value = (int)qt, name = qt.ToString(), description = GetQuotaTypeDescription(qt) })
                .ToList();

            return Ok(quotaTypes);
        }

        /// <summary>
        /// 获取配额来源列表
        /// </summary>
        [HttpGet("quota-sources")]
        public IActionResult GetQuotaSources()
        {
            var quotaSources = Enum.GetValues<QuotaSource>()
                .Select(qs => new { value = (int)qs, name = qs.ToString(), description = GetQuotaSourceDescription(qs) })
                .ToList();

            return Ok(quotaSources);
        }

        /// <summary>
        /// 获取配额类型描述
        /// </summary>
        private string GetQuotaTypeDescription(QuotaType quotaType)
        {
            return quotaType switch
            {
                QuotaType.General => "通用配额 (按金额)",
                QuotaType.Token => "Token配额",
                QuotaType.Request => "请求次数配额",
                QuotaType.ImageGeneration => "图像生成配额",
                QuotaType.AudioProcessing => "音频处理配额",
                _ => quotaType.ToString()
            };
        }

        /// <summary>
        /// 获取配额来源描述
        /// </summary>
        private string GetQuotaSourceDescription(QuotaSource quotaSource)
        {
            return quotaSource switch
            {
                QuotaSource.SystemGrant => "系统赠送",
                QuotaSource.UserRecharge => "用户充值",
                QuotaSource.AdminAllocation => "管理员分配",
                QuotaSource.InvitationReward => "邀请奖励",
                QuotaSource.ActivityReward => "活动奖励",
                QuotaSource.RedeemCode => "充值码兑换",
                _ => quotaSource.ToString()
            };
        }
    }

    // Request DTOs
    public class EstimateCostRequest
    {
        public string ModelName { get; set; } = string.Empty;
        public int EstimatedInputTokens { get; set; }
        public int EstimatedOutputTokens { get; set; }
    }

    public class CheckQuotaRequest
    {
        public string ModelName { get; set; } = string.Empty;
        public int EstimatedInputTokens { get; set; }
        public int EstimatedOutputTokens { get; set; }
    }
}
