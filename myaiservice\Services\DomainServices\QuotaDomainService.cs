using myaiservice.Entities;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 配额领域服务
    /// </summary>
    public class QuotaDomainService : DomainService
    {
        private readonly IRepository<Quota, Guid> _quotaRepository;
        private readonly IRepository<QuotaTransaction, Guid> _quotaTransactionRepository;
        private readonly IRepository<UserProfile, Guid> _userProfileRepository;

        public QuotaDomainService(
            IRepository<Quota, Guid> quotaRepository,
            IRepository<QuotaTransaction, Guid> quotaTransactionRepository,
            IRepository<UserProfile, Guid> userProfileRepository)
        {
            _quotaRepository = quotaRepository;
            _quotaTransactionRepository = quotaTransactionRepository;
            _userProfileRepository = userProfileRepository;
        }

        /// <summary>
        /// 为用户创建配额
        /// </summary>
        public async Task<Quota> CreateQuotaAsync(Guid userId, QuotaType type, decimal totalQuota, QuotaSource source, DateTime? expiresAt = null, string? description = null)
        {
            var quota = new Quota(GuidGenerator.Create(), userId, type, totalQuota, source, expiresAt)
            {
                SourceDescription = description
            };

            await _quotaRepository.InsertAsync(quota);

            // 记录配额交易
            await RecordQuotaTransactionAsync(userId, quota.Id, QuotaTransactionType.Recharge, totalQuota, 0, totalQuota, description);

            return quota;
        }

        /// <summary>
        /// 检查用户配额是否足够
        /// </summary>
        public async Task<(bool HasSufficient, decimal Available)> CheckUserQuotaAsync(Guid userId, QuotaType type, decimal requiredQuota)
        {
            var userQuotas = await _quotaRepository.GetListAsync(q => 
                q.UserId == userId && 
                q.Type == type && 
                !q.IsExpired);

            var totalAvailable = userQuotas.Where(q => q.IsUnlimited).Any() 
                ? decimal.MaxValue 
                : userQuotas.Sum(q => q.RemainingQuota);

            return (totalAvailable >= requiredQuota, totalAvailable);
        }

        /// <summary>
        /// 消费用户配额
        /// </summary>
        public async Task<bool> ConsumeUserQuotaAsync(Guid userId, QuotaType type, decimal amount, string? description = null, Guid? tokenId = null, Guid? requestLogId = null)
        {
            var userQuotas = await _quotaRepository.GetListAsync(q => 
                q.UserId == userId && 
                q.Type == type && 
                !q.IsExpired);

            // 优先使用即将过期的配额
            userQuotas = userQuotas.OrderBy(q => q.ExpiresAt ?? DateTime.MaxValue).ToList();

            var remainingAmount = amount;
            var consumedQuotas = new List<Quota>();

            foreach (var quota in userQuotas)
            {
                if (remainingAmount <= 0) break;

                var balanceBefore = quota.RemainingQuota;
                var consumeAmount = Math.Min(remainingAmount, quota.RemainingQuota);

                if (quota.ConsumeQuota(consumeAmount))
                {
                    consumedQuotas.Add(quota);
                    remainingAmount -= consumeAmount;

                    // 记录配额交易
                    await RecordQuotaTransactionAsync(userId, quota.Id, QuotaTransactionType.Consumption, 
                        consumeAmount, balanceBefore, quota.RemainingQuota, description, tokenId, requestLogId);
                }
            }

            if (remainingAmount > 0)
                return false; // 配额不足

            // 更新配额
            await _quotaRepository.UpdateManyAsync(consumedQuotas);

            // 更新用户资料中的配额信息
            await UpdateUserProfileQuotaAsync(userId);

            return true;
        }

        /// <summary>
        /// 为用户充值配额
        /// </summary>
        public async Task<Quota> RechargeUserQuotaAsync(Guid userId, QuotaType type, decimal amount, QuotaSource source, DateTime? expiresAt = null, string? description = null)
        {
            // 查找用户现有的同类型配额
            var existingQuota = await _quotaRepository.FirstOrDefaultAsync(q => 
                q.UserId == userId && 
                q.Type == type && 
                q.Source == source && 
                !q.IsExpired);

            if (existingQuota != null)
            {
                // 增加到现有配额
                var balanceBefore = existingQuota.RemainingQuota;
                existingQuota.AddQuota(amount);
                await _quotaRepository.UpdateAsync(existingQuota);

                // 记录配额交易
                await RecordQuotaTransactionAsync(userId, existingQuota.Id, QuotaTransactionType.Recharge, 
                    amount, balanceBefore, existingQuota.RemainingQuota, description);

                await UpdateUserProfileQuotaAsync(userId);
                return existingQuota;
            }
            else
            {
                // 创建新配额
                return await CreateQuotaAsync(userId, type, amount, source, expiresAt, description);
            }
        }

        /// <summary>
        /// 退款配额
        /// </summary>
        public async Task RefundUserQuotaAsync(Guid userId, QuotaType type, decimal amount, string? description = null)
        {
            await RechargeUserQuotaAsync(userId, type, amount, QuotaSource.AdminAllocation, null, description ?? "Refund");
        }

        /// <summary>
        /// 管理员调整配额
        /// </summary>
        public async Task AdminAdjustQuotaAsync(Guid userId, QuotaType type, decimal newTotalQuota, string? description = null)
        {
            var userQuotas = await _quotaRepository.GetListAsync(q => 
                q.UserId == userId && 
                q.Type == type && 
                !q.IsExpired);

            var currentTotal = userQuotas.Sum(q => q.TotalQuota);
            var adjustment = newTotalQuota - currentTotal;

            if (adjustment != 0)
            {
                if (adjustment > 0)
                {
                    // 增加配额
                    await RechargeUserQuotaAsync(userId, type, adjustment, QuotaSource.AdminAllocation, null, description);
                }
                else
                {
                    // 减少配额 - 这里需要更复杂的逻辑
                    await ReduceUserQuotaAsync(userId, type, Math.Abs(adjustment), description);
                }
            }
        }

        /// <summary>
        /// 减少用户配额
        /// </summary>
        private async Task ReduceUserQuotaAsync(Guid userId, QuotaType type, decimal amount, string? description = null)
        {
            var userQuotas = await _quotaRepository.GetListAsync(q => 
                q.UserId == userId && 
                q.Type == type && 
                !q.IsExpired);

            // 优先从剩余配额最多的开始减少
            userQuotas = userQuotas.OrderByDescending(q => q.RemainingQuota).ToList();

            var remainingAmount = amount;
            var adjustedQuotas = new List<Quota>();

            foreach (var quota in userQuotas)
            {
                if (remainingAmount <= 0) break;

                var balanceBefore = quota.TotalQuota;
                var reduceAmount = Math.Min(remainingAmount, quota.TotalQuota);

                quota.TotalQuota -= reduceAmount;
                if (quota.UsedQuota > quota.TotalQuota)
                {
                    quota.UsedQuota = quota.TotalQuota;
                }

                adjustedQuotas.Add(quota);
                remainingAmount -= reduceAmount;

                // 记录配额交易
                await RecordQuotaTransactionAsync(userId, quota.Id, QuotaTransactionType.AdminAdjustment, 
                    -reduceAmount, balanceBefore, quota.TotalQuota, description);
            }

            await _quotaRepository.UpdateManyAsync(adjustedQuotas);
            await UpdateUserProfileQuotaAsync(userId);
        }

        /// <summary>
        /// 记录配额交易
        /// </summary>
        private async Task RecordQuotaTransactionAsync(Guid userId, Guid quotaId, QuotaTransactionType type, 
            decimal amount, decimal balanceBefore, decimal balanceAfter, string? description = null, 
            Guid? tokenId = null, Guid? requestLogId = null)
        {
            var transaction = new QuotaTransaction(
                GuidGenerator.Create(), userId, quotaId, type, amount, balanceBefore, balanceAfter, description)
            {
                TokenId = tokenId,
                RequestLogId = requestLogId
            };

            await _quotaTransactionRepository.InsertAsync(transaction);
        }

        /// <summary>
        /// 更新用户资料中的配额信息
        /// </summary>
        private async Task UpdateUserProfileQuotaAsync(Guid userId)
        {
            var userProfile = await _userProfileRepository.FirstOrDefaultAsync(up => up.UserId == userId);
            if (userProfile == null) return;

            var userQuotas = await _quotaRepository.GetListAsync(q => 
                q.UserId == userId && 
                q.Type == QuotaType.General && 
                !q.IsExpired);

            userProfile.TotalQuota = userQuotas.Sum(q => q.TotalQuota);
            userProfile.UsedQuota = userQuotas.Sum(q => q.UsedQuota);
            userProfile.IsUnlimitedQuota = userQuotas.Any(q => q.IsUnlimited);

            await _userProfileRepository.UpdateAsync(userProfile);
        }

        /// <summary>
        /// 获取用户配额统计
        /// </summary>
        public async Task<UserQuotaStatistics> GetUserQuotaStatisticsAsync(Guid userId)
        {
            var userQuotas = await _quotaRepository.GetListAsync(q => q.UserId == userId && !q.IsExpired);
            
            var statistics = new UserQuotaStatistics
            {
                UserId = userId,
                QuotaDetails = new List<QuotaDetail>()
            };

            foreach (var quotaType in Enum.GetValues<QuotaType>())
            {
                var typeQuotas = userQuotas.Where(q => q.Type == quotaType).ToList();
                if (typeQuotas.Any())
                {
                    statistics.QuotaDetails.Add(new QuotaDetail
                    {
                        Type = quotaType,
                        TotalQuota = typeQuotas.Sum(q => q.TotalQuota),
                        UsedQuota = typeQuotas.Sum(q => q.UsedQuota),
                        RemainingQuota = typeQuotas.Sum(q => q.RemainingQuota),
                        IsUnlimited = typeQuotas.Any(q => q.IsUnlimited),
                        ExpiringQuota = typeQuotas.Where(q => q.ExpiresAt.HasValue && q.ExpiresAt.Value <= DateTime.UtcNow.AddDays(7)).Sum(q => q.RemainingQuota)
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// 清理过期配额
        /// </summary>
        public async Task CleanupExpiredQuotasAsync()
        {
            var expiredQuotas = await _quotaRepository.GetListAsync(q => q.IsExpired && q.RemainingQuota > 0);

            foreach (var quota in expiredQuotas)
            {
                if (quota.RemainingQuota > 0)
                {
                    // 记录过期扣除
                    await RecordQuotaTransactionAsync(quota.UserId, quota.Id, QuotaTransactionType.Expiration, 
                        quota.RemainingQuota, quota.RemainingQuota, 0, "Quota expired");
                    
                    quota.UsedQuota = quota.TotalQuota; // 将剩余配额标记为已使用
                }
            }

            await _quotaRepository.UpdateManyAsync(expiredQuotas);
        }
    }

    /// <summary>
    /// 用户配额统计
    /// </summary>
    public class UserQuotaStatistics
    {
        public Guid UserId { get; set; }
        public List<QuotaDetail> QuotaDetails { get; set; } = new();
    }

    /// <summary>
    /// 配额详情
    /// </summary>
    public class QuotaDetail
    {
        public QuotaType Type { get; set; }
        public decimal TotalQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public decimal RemainingQuota { get; set; }
        public bool IsUnlimited { get; set; }
        public decimal ExpiringQuota { get; set; }
    }
}
