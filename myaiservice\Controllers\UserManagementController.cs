using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Entities;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserManagementController : AbpControllerBase
    {
        private readonly UserManagementAppService _userManagementAppService;

        public UserManagementController(UserManagementAppService userManagementAppService)
        {
            _userManagementAppService = userManagementAppService;
        }

        /// <summary>
        /// 创建用户资料
        /// </summary>
        [HttpPost("profile")]
        [Authorize(Roles = "admin")]
        public async Task<UserProfileDto> CreateUserProfileAsync([FromBody] CreateUserProfileDto input)
        {
            return await _userManagementAppService.CreateUserProfileAsync(input);
        }

        /// <summary>
        /// 获取当前用户资料
        /// </summary>
        [HttpGet("profile")]
        public async Task<UserProfileDto> GetMyProfileAsync()
        {
            var userId = CurrentUser.Id!.Value;
            return await _userManagementAppService.GetUserProfileAsync(userId);
        }

        /// <summary>
        /// 获取指定用户资料
        /// </summary>
        [HttpGet("profile/{userId}")]
        [Authorize(Roles = "admin")]
        public async Task<UserProfileDto> GetUserProfileAsync(Guid userId)
        {
            return await _userManagementAppService.GetUserProfileAsync(userId);
        }

        /// <summary>
        /// 更新当前用户资料
        /// </summary>
        [HttpPut("profile")]
        public async Task<UserProfileDto> UpdateMyProfileAsync([FromBody] UpdateUserProfileDto input)
        {
            var userId = CurrentUser.Id!.Value;
            return await _userManagementAppService.UpdateUserProfileAsync(userId, input);
        }

        /// <summary>
        /// 更新指定用户资料
        /// </summary>
        [HttpPut("profile/{userId}")]
        [Authorize(Roles = "admin")]
        public async Task<UserProfileDto> UpdateUserProfileAsync(Guid userId, [FromBody] UpdateUserProfileDto input)
        {
            return await _userManagementAppService.UpdateUserProfileAsync(userId, input);
        }

        /// <summary>
        /// 生成邀请码
        /// </summary>
        [HttpPost("invitation-codes")]
        public async Task<InvitationCodeDto> GenerateInvitationCodeAsync([FromBody] GenerateInvitationCodeDto input)
        {
            return await _userManagementAppService.GenerateInvitationCodeAsync(input);
        }

        /// <summary>
        /// 使用邀请码
        /// </summary>
        [HttpPost("invitation-codes/{code}/use")]
        public async Task<IActionResult> UseInvitationCodeAsync(string code)
        {
            var result = await _userManagementAppService.UseInvitationCodeAsync(code);
            if (result)
            {
                return Ok(new { success = true, message = "Invitation code used successfully" });
            }
            return BadRequest(new { success = false, message = "Invalid or expired invitation code" });
        }

        /// <summary>
        /// 获取当前用户的邀请码列表
        /// </summary>
        [HttpGet("invitation-codes")]
        public async Task<List<InvitationCodeDto>> GetMyInvitationCodesAsync()
        {
            return await _userManagementAppService.GetUserInvitationCodesAsync();
        }

        /// <summary>
        /// 记录用户登录
        /// </summary>
        [HttpPost("login-record")]
        public async Task<IActionResult> RecordLoginAsync()
        {
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            await _userManagementAppService.RecordUserLoginAsync(ipAddress);
            return Ok();
        }

        /// <summary>
        /// 设置用户状态
        /// </summary>
        [HttpPut("{userId}/status")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> SetUserStatusAsync(Guid userId, [FromBody] SetUserStatusRequest request)
        {
            await _userManagementAppService.SetUserStatusAsync(userId, request.Status);
            return Ok();
        }

        /// <summary>
        /// 为用户充值配额
        /// </summary>
        [HttpPost("{userId}/recharge")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> RechargeUserQuotaAsync(Guid userId, [FromBody] RechargeQuotaRequest request)
        {
            await _userManagementAppService.RechargeUserQuotaAsync(userId, request.Amount, request.Description);
            return Ok();
        }

        /// <summary>
        /// 获取当前用户统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<UserStatisticsDto> GetMyStatisticsAsync()
        {
            var userId = CurrentUser.Id!.Value;
            return await _userManagementAppService.GetUserStatisticsAsync(userId);
        }

        /// <summary>
        /// 获取指定用户统计信息
        /// </summary>
        [HttpGet("{userId}/statistics")]
        [Authorize(Roles = "admin")]
        public async Task<UserStatisticsDto> GetUserStatisticsAsync(Guid userId)
        {
            return await _userManagementAppService.GetUserStatisticsAsync(userId);
        }
    }

    // Request DTOs
    public class SetUserStatusRequest
    {
        public UserStatus Status { get; set; }
    }

    public class RechargeQuotaRequest
    {
        public decimal Amount { get; set; }
        public string? Description { get; set; }
    }
}
