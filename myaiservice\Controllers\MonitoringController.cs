using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.ApplicationServices;
using myaiservice.Services.DomainServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 监控控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MonitoringController : AbpControllerBase
    {
        private readonly MonitoringAppService _monitoringAppService;

        public MonitoringController(MonitoringAppService monitoringAppService)
        {
            _monitoringAppService = monitoringAppService;
        }

        /// <summary>
        /// 获取系统总览统计
        /// </summary>
        [HttpGet("overview")]
        [Authorize(Roles = "admin")]
        public async Task<SystemOverviewStatistics> GetSystemOverviewAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetSystemOverviewAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取请求趋势统计
        /// </summary>
        [HttpPost("request-trend")]
        [Authorize(Roles = "admin")]
        public async Task<List<RequestTrendStatistics>> GetRequestTrendAsync([FromBody] GetRequestTrendDto input)
        {
            return await _monitoringAppService.GetRequestTrendAsync(input);
        }

        /// <summary>
        /// 获取模型使用统计
        /// </summary>
        [HttpGet("model-usage")]
        [Authorize(Roles = "admin")]
        public async Task<List<ModelUsageStatistics>> GetModelUsageStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetModelUsageStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取渠道性能统计
        /// </summary>
        [HttpGet("channel-performance")]
        [Authorize(Roles = "admin")]
        public async Task<List<ChannelPerformanceStatistics>> GetChannelPerformanceStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetChannelPerformanceStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取用户活跃度统计
        /// </summary>
        [HttpPost("user-activity")]
        [Authorize(Roles = "admin")]
        public async Task<List<UserActivityStatistics>> GetUserActivityStatisticsAsync([FromBody] GetUserActivityDto input)
        {
            return await _monitoringAppService.GetUserActivityStatisticsAsync(input);
        }

        /// <summary>
        /// 获取错误分析统计
        /// </summary>
        [HttpGet("error-analysis")]
        [Authorize(Roles = "admin")]
        public async Task<List<ErrorAnalysisStatistics>> GetErrorAnalysisAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetErrorAnalysisAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取实时监控数据
        /// </summary>
        [HttpGet("realtime")]
        [Authorize(Roles = "admin")]
        public async Task<RealTimeMonitoringData> GetRealTimeMonitoringDataAsync()
        {
            return await _monitoringAppService.GetRealTimeMonitoringDataAsync();
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        [HttpGet("performance")]
        [Authorize(Roles = "admin")]
        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetPerformanceMetricsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取当前用户的使用统计
        /// </summary>
        [HttpGet("my-usage")]
        public async Task<UserPersonalStatistics> GetMyUsageStatisticsAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            return await _monitoringAppService.GetMyUsageStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        [HttpGet("health")]
        [Authorize(Roles = "admin")]
        public async Task<SystemHealthStatus> GetSystemHealthAsync()
        {
            return await _monitoringAppService.GetSystemHealthAsync();
        }

        /// <summary>
        /// 获取系统告警
        /// </summary>
        [HttpGet("alerts")]
        [Authorize(Roles = "admin")]
        public async Task<List<SystemAlert>> GetSystemAlertsAsync()
        {
            return await _monitoringAppService.GetSystemAlertsAsync();
        }

        /// <summary>
        /// 导出统计报告
        /// </summary>
        [HttpPost("export-report")]
        [Authorize(Roles = "admin")]
        public async Task<StatisticsReport> ExportStatisticsReportAsync([FromBody] ExportReportDto input)
        {
            return await _monitoringAppService.ExportStatisticsReportAsync(input);
        }

        /// <summary>
        /// 获取仪表板数据
        /// </summary>
        [HttpGet("dashboard")]
        [Authorize(Roles = "admin")]
        public async Task<DashboardData> GetDashboardDataAsync()
        {
            var overview = await _monitoringAppService.GetSystemOverviewAsync(DateTime.UtcNow.Date.AddDays(-7), DateTime.UtcNow);
            var realTimeData = await _monitoringAppService.GetRealTimeMonitoringDataAsync();
            var health = await _monitoringAppService.GetSystemHealthAsync();
            var alerts = await _monitoringAppService.GetSystemAlertsAsync();
            var topModels = await _monitoringAppService.GetModelUsageStatisticsAsync(DateTime.UtcNow.Date.AddDays(-7), DateTime.UtcNow);

            return new DashboardData
            {
                Overview = overview,
                RealTimeData = realTimeData,
                HealthStatus = health,
                RecentAlerts = alerts.Take(5).ToList(),
                TopModels = topModels.Take(10).ToList(),
                LastUpdated = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 获取统计数据摘要
        /// </summary>
        [HttpGet("summary")]
        [Authorize(Roles = "admin")]
        public async Task<StatisticsSummary> GetStatisticsSummaryAsync([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var overview = await _monitoringAppService.GetSystemOverviewAsync(start, end);
            var modelStats = await _monitoringAppService.GetModelUsageStatisticsAsync(start, end);
            var channelStats = await _monitoringAppService.GetChannelPerformanceStatisticsAsync(start, end);
            var errorStats = await _monitoringAppService.GetErrorAnalysisAsync(start, end);

            return new StatisticsSummary
            {
                TotalRequests = overview.TotalRequests,
                SuccessRate = overview.SuccessRate,
                TotalRevenue = overview.TotalRevenue,
                ActiveUsers = overview.ActiveUsers,
                MostUsedModel = modelStats.FirstOrDefault()?.ModelName ?? "N/A",
                BestChannel = channelStats.OrderBy(c => c.ErrorRate).FirstOrDefault()?.ChannelName ?? "N/A",
                TopError = errorStats.FirstOrDefault()?.ErrorCode ?? "None",
                AverageResponseTime = overview.AverageResponseTime,
                StatisticsPeriod = new DateRange(start, end)
            };
        }

        /// <summary>
        /// 获取趋势对比数据
        /// </summary>
        [HttpGet("trend-comparison")]
        [Authorize(Roles = "admin")]
        public async Task<TrendComparisonData> GetTrendComparisonAsync([FromQuery] int days = 7)
        {
            var currentPeriodEnd = DateTime.UtcNow.Date;
            var currentPeriodStart = currentPeriodEnd.AddDays(-days);
            var previousPeriodEnd = currentPeriodStart;
            var previousPeriodStart = previousPeriodEnd.AddDays(-days);

            var currentStats = await _monitoringAppService.GetSystemOverviewAsync(currentPeriodStart, currentPeriodEnd);
            var previousStats = await _monitoringAppService.GetSystemOverviewAsync(previousPeriodStart, previousPeriodEnd);

            return new TrendComparisonData
            {
                CurrentPeriod = currentStats,
                PreviousPeriod = previousStats,
                RequestsChange = CalculatePercentageChange(previousStats.TotalRequests, currentStats.TotalRequests),
                SuccessRateChange = currentStats.SuccessRate - previousStats.SuccessRate,
                RevenueChange = CalculatePercentageChange(previousStats.TotalRevenue, currentStats.TotalRevenue),
                ResponseTimeChange = CalculatePercentageChange((decimal)previousStats.AverageResponseTime, (decimal)currentStats.AverageResponseTime),
                ComparisonPeriodDays = days
            };
        }

        private double CalculatePercentageChange(decimal oldValue, decimal newValue)
        {
            if (oldValue == 0) return newValue > 0 ? 100 : 0;
            return (double)((newValue - oldValue) / oldValue * 100);
        }

        private double CalculatePercentageChange(long oldValue, long newValue)
        {
            if (oldValue == 0) return newValue > 0 ? 100 : 0;
            return (double)((newValue - oldValue) / (double)oldValue * 100);
        }
    }

    // 响应DTOs
    public class DashboardData
    {
        public SystemOverviewStatistics Overview { get; set; } = new();
        public RealTimeMonitoringData RealTimeData { get; set; } = new();
        public SystemHealthStatus HealthStatus { get; set; } = new();
        public List<SystemAlert> RecentAlerts { get; set; } = new();
        public List<ModelUsageStatistics> TopModels { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    public class StatisticsSummary
    {
        public long TotalRequests { get; set; }
        public double SuccessRate { get; set; }
        public decimal TotalRevenue { get; set; }
        public int ActiveUsers { get; set; }
        public string MostUsedModel { get; set; } = string.Empty;
        public string BestChannel { get; set; } = string.Empty;
        public string TopError { get; set; } = string.Empty;
        public double AverageResponseTime { get; set; }
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public class TrendComparisonData
    {
        public SystemOverviewStatistics CurrentPeriod { get; set; } = new();
        public SystemOverviewStatistics PreviousPeriod { get; set; } = new();
        public double RequestsChange { get; set; }
        public double SuccessRateChange { get; set; }
        public double RevenueChange { get; set; }
        public double ResponseTimeChange { get; set; }
        public int ComparisonPeriodDays { get; set; }
    }
}
