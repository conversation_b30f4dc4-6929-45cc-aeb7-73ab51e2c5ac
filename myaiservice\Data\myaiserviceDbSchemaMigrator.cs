﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace myaiservice.Data;

public class myaiserviceDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public myaiserviceDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the myaiserviceDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<myaiserviceDbContext>()
            .Database
            .MigrateAsync();

    }
}
