﻿using MagicOnion.Client;
using myaiservice.Entities;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Services;

public class APIKeyManagementAppService(
        IRepository<Key> keyRepository
    ) : myaiserviceAppService
{

    /// <summary>
    /// 添加API密钥
    /// </summary>
    /// <returns></returns>
    public async Task AddApiKey(string keyType,List<string> keyValue)
    {
        var key = new Key
        {
            Name = "API Key",
            KeyType = keyType,
            KeyValues = keyValue
        };
        await keyRepository.InsertAsync(key);
    }
}
