using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 请求日志实体 - API请求记录聚合根
    /// </summary>
    public class RequestLog : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 令牌ID
        /// </summary>
        public Guid? TokenId { get; set; }

        /// <summary>
        /// 渠道ID
        /// </summary>
        public Guid? ChannelId { get; set; }

        /// <summary>
        /// 模型ID
        /// </summary>
        public Guid? ModelId { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        [Required]
        [StringLength(200)]
        public string RequestPath { get; set; } = string.Empty;

        /// <summary>
        /// 请求方法
        /// </summary>
        [Required]
        [StringLength(10)]
        public string RequestMethod { get; set; } = string.Empty;

        /// <summary>
        /// 请求IP地址
        /// </summary>
        [StringLength(45)]
        public string? RequestIp { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 请求头 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? RequestHeaders { get; set; }

        /// <summary>
        /// 请求体 (JSON格式)
        /// </summary>
        public string? RequestBody { get; set; }

        /// <summary>
        /// 响应状态码
        /// </summary>
        public int ResponseStatusCode { get; set; }

        /// <summary>
        /// 响应头 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? ResponseHeaders { get; set; }

        /// <summary>
        /// 响应体 (JSON格式)
        /// </summary>
        public string? ResponseBody { get; set; }

        /// <summary>
        /// 请求开始时间
        /// </summary>
        public DateTime RequestStartTime { get; set; }

        /// <summary>
        /// 请求结束时间
        /// </summary>
        public DateTime RequestEndTime { get; set; }

        /// <summary>
        /// 响应时间 (毫秒)
        /// </summary>
        public double ResponseTime { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        [StringLength(50)]
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        [StringLength(100)]
        public string? ModelName { get; set; }

        /// <summary>
        /// 输入Token数量
        /// </summary>
        public long InputTokens { get; set; } = 0;

        /// <summary>
        /// 输出Token数量
        /// </summary>
        public long OutputTokens { get; set; } = 0;

        /// <summary>
        /// 总Token数量
        /// </summary>
        public long TotalTokens => InputTokens + OutputTokens;

        /// <summary>
        /// 缓存Token数量
        /// </summary>
        public long CacheTokens { get; set; } = 0;

        /// <summary>
        /// 请求成本
        /// </summary>
        public decimal Cost { get; set; } = 0;

        /// <summary>
        /// 是否流式响应
        /// </summary>
        public bool IsStreaming { get; set; } = false;

        /// <summary>
        /// 流式响应块数量
        /// </summary>
        public int StreamChunks { get; set; } = 0;

        /// <summary>
        /// 请求类型
        /// </summary>
        public RequestType Type { get; set; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public ContentType ContentType { get; set; }

        /// <summary>
        /// 文件数量 (多模态请求)
        /// </summary>
        public int FileCount { get; set; } = 0;

        /// <summary>
        /// 文件大小 (字节)
        /// </summary>
        public long FileSize { get; set; } = 0;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 渠道名称
        /// </summary>
        [StringLength(100)]
        public string? ChannelName { get; set; }

        /// <summary>
        /// 渠道类型
        /// </summary>
        [StringLength(50)]
        public string? ChannelType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public RequestLog()
        {
        }

        public RequestLog(Guid id, string requestPath, string requestMethod, RequestType type)
        {
            Id = id;
            RequestPath = requestPath;
            RequestMethod = requestMethod;
            Type = type;
            RequestStartTime = DateTime.UtcNow;
            IsSuccess = false;
            ResponseStatusCode = 0;
            InputTokens = 0;
            OutputTokens = 0;
            CacheTokens = 0;
            Cost = 0;
            IsStreaming = false;
            StreamChunks = 0;
            ContentType = ContentType.Text;
            FileCount = 0;
            FileSize = 0;
            RetryCount = 0;
        }

        /// <summary>
        /// 设置请求信息
        /// </summary>
        public void SetRequestInfo(string? requestIp, string? userAgent, string? requestHeaders, string? requestBody)
        {
            RequestIp = requestIp;
            UserAgent = userAgent;
            RequestHeaders = requestHeaders;
            RequestBody = requestBody;
        }

        /// <summary>
        /// 设置响应信息
        /// </summary>
        public void SetResponseInfo(int statusCode, string? responseHeaders, string? responseBody, bool isSuccess, string? errorMessage = null, string? errorCode = null)
        {
            RequestEndTime = DateTime.UtcNow;
            ResponseTime = (RequestEndTime - RequestStartTime).TotalMilliseconds;
            ResponseStatusCode = statusCode;
            ResponseHeaders = responseHeaders;
            ResponseBody = responseBody;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
            ErrorCode = errorCode;
        }

        /// <summary>
        /// 设置Token使用信息
        /// </summary>
        public void SetTokenUsage(long inputTokens, long outputTokens, long cacheTokens = 0)
        {
            InputTokens = inputTokens;
            OutputTokens = outputTokens;
            CacheTokens = cacheTokens;
        }

        /// <summary>
        /// 设置成本信息
        /// </summary>
        public void SetCost(decimal cost)
        {
            Cost = cost;
        }

        /// <summary>
        /// 设置模型信息
        /// </summary>
        public void SetModelInfo(Guid? modelId, string? modelName)
        {
            ModelId = modelId;
            ModelName = modelName;
        }

        /// <summary>
        /// 设置渠道信息
        /// </summary>
        public void SetChannelInfo(Guid? channelId, string? channelName, string? channelType)
        {
            ChannelId = channelId;
            ChannelName = channelName;
            ChannelType = channelType;
        }

        /// <summary>
        /// 设置用户信息
        /// </summary>
        public void SetUserInfo(Guid? userId, Guid? tokenId)
        {
            UserId = userId;
            TokenId = tokenId;
        }

        /// <summary>
        /// 设置流式响应信息
        /// </summary>
        public void SetStreamingInfo(bool isStreaming, int streamChunks = 0)
        {
            IsStreaming = isStreaming;
            StreamChunks = streamChunks;
        }

        /// <summary>
        /// 设置文件信息
        /// </summary>
        public void SetFileInfo(int fileCount, long fileSize, ContentType contentType)
        {
            FileCount = fileCount;
            FileSize = fileSize;
            ContentType = contentType;
        }

        /// <summary>
        /// 增加重试次数
        /// </summary>
        public void IncrementRetryCount()
        {
            RetryCount++;
        }

        /// <summary>
        /// 获取请求持续时间
        /// </summary>
        public TimeSpan GetDuration()
        {
            return RequestEndTime - RequestStartTime;
        }

        /// <summary>
        /// 检查是否为成功请求
        /// </summary>
        public bool IsSuccessfulRequest()
        {
            return IsSuccess && ResponseStatusCode >= 200 && ResponseStatusCode < 300;
        }
    }

    /// <summary>
    /// 请求类型枚举
    /// </summary>
    public enum RequestType
    {
        /// <summary>
        /// 聊天补全
        /// </summary>
        ChatCompletion = 1,

        /// <summary>
        /// 文本补全
        /// </summary>
        TextCompletion = 2,

        /// <summary>
        /// 文本嵌入
        /// </summary>
        Embedding = 3,

        /// <summary>
        /// 图像生成
        /// </summary>
        ImageGeneration = 4,

        /// <summary>
        /// 语音识别
        /// </summary>
        SpeechToText = 5,

        /// <summary>
        /// 语音合成
        /// </summary>
        TextToSpeech = 6,

        /// <summary>
        /// 重排序
        /// </summary>
        Rerank = 7,

        /// <summary>
        /// 其他
        /// </summary>
        Other = 99
    }

    /// <summary>
    /// 内容类型枚举
    /// </summary>
    public enum ContentType
    {
        /// <summary>
        /// 纯文本
        /// </summary>
        Text = 1,

        /// <summary>
        /// 图像
        /// </summary>
        Image = 2,

        /// <summary>
        /// 音频
        /// </summary>
        Audio = 3,

        /// <summary>
        /// 视频
        /// </summary>
        Video = 4,

        /// <summary>
        /// 多模态
        /// </summary>
        Multimodal = 5
    }
}
