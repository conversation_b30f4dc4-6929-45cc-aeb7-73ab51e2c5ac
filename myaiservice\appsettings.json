{"App": {"SelfUrl": "https://localhost:44356", "ClientUrl": "http://localhost:4200", "CorsOrigins": "https://*.myaiservice.com,http://localhost:4200", "RedirectAllowedUrls": "http://localhost:4200", "HealthCheckUrl": "/health-status"}, "ConnectionStrings": {"Default": "Data Source=sql.db3"}, "AuthServer": {"Authority": "https://localhost:44356", "SwaggerClientId": "myaiservice_Swagger", "CertificatePassPhrase": "83599f40-544c-46fa-adc0-87aefa91d8a8"}, "Settings": {"Abp.Identity.Password.RequireNonAlphanumeric": "false", "Abp.Identity.Password.RequireLowercase": "false", "Abp.Identity.Password.RequireUppercase": "false", "Abp.Identity.Password.RequireDigit": "false"}, "StringEncryption": {"DefaultPassPhrase": "wZluHAAw8zBMl34Z"}, "OpenIddict": {"Applications": {"myaiservice_App": {"ClientId": "myaiservice_App", "RootUrl": "http://localhost:4200"}, "myaiservice_Swagger": {"ClientId": "myaiservice_Swagger", "RootUrl": "https://localhost:44356"}}}}