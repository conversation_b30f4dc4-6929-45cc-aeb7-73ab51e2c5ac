using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 日志管理应用服务
    /// </summary>
    [Authorize]
    public class LogManagementAppService : ApplicationService
    {
        private readonly IRepository<RequestLog, Guid> _requestLogRepository;

        public LogManagementAppService(IRepository<RequestLog, Guid> requestLogRepository)
        {
            _requestLogRepository = requestLogRepository;
        }

        /// <summary>
        /// 获取请求日志列表
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<RequestLogDto>> GetRequestLogsAsync(GetRequestLogsDto input)
        {
            var queryable = await _requestLogRepository.GetQueryableAsync();
            var query = queryable.AsQueryable();

            // 应用过滤条件
            if (input.StartDate.HasValue)
                query = query.Where(r => r.CreationTime >= input.StartDate.Value);

            if (input.EndDate.HasValue)
                query = query.Where(r => r.CreationTime <= input.EndDate.Value);

            if (input.UserId.HasValue)
                query = query.Where(r => r.UserId == input.UserId.Value);

            if (input.TokenId.HasValue)
                query = query.Where(r => r.TokenId == input.TokenId.Value);

            if (!string.IsNullOrEmpty(input.ModelName))
                query = query.Where(r => r.ModelName.Contains(input.ModelName));

            if (!string.IsNullOrEmpty(input.Endpoint))
                query = query.Where(r => r.RequestPath.Contains(input.Endpoint));

            if (input.IsSuccess.HasValue)
                query = query.Where(r => r.IsSuccess == input.IsSuccess.Value);

            if (input.MinResponseTime.HasValue)
                query = query.Where(r => r.ResponseTime >= input.MinResponseTime.Value);

            if (input.MaxResponseTime.HasValue)
                query = query.Where(r => r.ResponseTime <= input.MaxResponseTime.Value);

            if (!string.IsNullOrEmpty(input.ErrorCode))
                query = query.Where(r => r.ErrorCode == input.ErrorCode);

            if (!string.IsNullOrEmpty(input.IpAddress))
                query = query.Where(r => r.RequestIp == input.IpAddress);

            // 排序
            query = input.SortBy?.ToLower() switch
            {
                "creationtime" => input.SortDirection == "desc" 
                    ? query.OrderByDescending(r => r.CreationTime)
                    : query.OrderBy(r => r.CreationTime),
                "responsetime" => input.SortDirection == "desc"
                    ? query.OrderByDescending(r => r.ResponseTime)
                    : query.OrderBy(r => r.ResponseTime),
                "cost" => input.SortDirection == "desc"
                    ? query.OrderByDescending(r => r.Cost)
                    : query.OrderBy(r => r.Cost),
                _ => query.OrderByDescending(r => r.CreationTime)
            };

            // 分页
            var logs = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount)
                     .Take(input.MaxResultCount));

            return ObjectMapper.Map<List<RequestLog>, List<RequestLogDto>>(logs);
        }

        /// <summary>
        /// 获取请求日志详情
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<RequestLogDetailDto> GetRequestLogDetailAsync(Guid id)
        {
            var log = await _requestLogRepository.GetAsync(id);
            return ObjectMapper.Map<RequestLog, RequestLogDetailDto>(log);
        }

        /// <summary>
        /// 获取当前用户的请求日志
        /// </summary>
        public async Task<List<RequestLogDto>> GetMyRequestLogsAsync(GetMyRequestLogsDto input)
        {
            var userId = CurrentUser.Id!.Value;
            var queryable = await _requestLogRepository.GetQueryableAsync();
            var query = queryable.Where(r => r.UserId == userId);

            // 应用过滤条件
            if (input.StartDate.HasValue)
                query = query.Where(r => r.CreationTime >= input.StartDate.Value);

            if (input.EndDate.HasValue)
                query = query.Where(r => r.CreationTime <= input.EndDate.Value);

            if (!string.IsNullOrEmpty(input.ModelName))
                query = query.Where(r => r.ModelName.Contains(input.ModelName));

            if (input.IsSuccess.HasValue)
                query = query.Where(r => r.IsSuccess == input.IsSuccess.Value);

            // 排序和分页
            query = query.OrderByDescending(r => r.CreationTime);
            var logs = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount)
                     .Take(input.MaxResultCount));

            return ObjectMapper.Map<List<RequestLog>, List<RequestLogDto>>(logs);
        }

        /// <summary>
        /// 删除过期日志
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<int> DeleteExpiredLogsAsync(int retentionDays = 90)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
            var queryable = await _requestLogRepository.GetQueryableAsync();
            var expiredLogs = queryable.Where(r => r.CreationTime < cutoffDate);
            
            var count = await AsyncExecuter.CountAsync(expiredLogs);
            await _requestLogRepository.DeleteAsync(r => r.CreationTime < cutoffDate);
            
            return count;
        }

        /// <summary>
        /// 导出请求日志
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<LogExportResult> ExportRequestLogsAsync(ExportRequestLogsDto input)
        {
            var queryable = await _requestLogRepository.GetQueryableAsync();
            var query = queryable.AsQueryable();

            // 应用过滤条件
            if (input.StartDate.HasValue)
                query = query.Where(r => r.CreationTime >= input.StartDate.Value);

            if (input.EndDate.HasValue)
                query = query.Where(r => r.CreationTime <= input.EndDate.Value);

            if (input.UserId.HasValue)
                query = query.Where(r => r.UserId == input.UserId.Value);

            if (!string.IsNullOrEmpty(input.ModelName))
                query = query.Where(r => r.ModelName.Contains(input.ModelName));

            if (input.IsSuccess.HasValue)
                query = query.Where(r => r.IsSuccess == input.IsSuccess.Value);

            // 限制导出数量
            var maxExportCount = 10000;
            query = query.OrderByDescending(r => r.CreationTime).Take(maxExportCount);

            var logs = await AsyncExecuter.ToListAsync(query);
            var exportData = ObjectMapper.Map<List<RequestLog>, List<RequestLogExportDto>>(logs);

            return new LogExportResult
            {
                ExportId = Guid.NewGuid(),
                ExportedAt = DateTime.UtcNow,
                TotalRecords = exportData.Count,
                Data = exportData,
                Format = input.Format,
                FileName = $"request_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{input.Format.ToString().ToLower()}"
            };
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<LogStatistics> GetLogStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var queryable = await _requestLogRepository.GetQueryableAsync();
            var logs = queryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var totalLogs = await AsyncExecuter.CountAsync(logs);
            var successfulLogs = await AsyncExecuter.CountAsync(logs.Where(r => r.IsSuccess));
            var failedLogs = totalLogs - successfulLogs;

            var avgResponseTime = totalLogs > 0
                ? await AsyncExecuter.AverageAsync(logs, r => r.ResponseTime)
                : 0;

            var totalCost = await AsyncExecuter.SumAsync(logs, r => r.Cost);
            var totalTokens = await AsyncExecuter.SumAsync(logs, r => r.InputTokens + r.OutputTokens);

            var topModels = await AsyncExecuter.ToListAsync(
                logs.GroupBy(r => r.ModelName)
                    .Select(g => new { Model = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(10));

            var topErrors = await AsyncExecuter.ToListAsync(
                logs.Where(r => !r.IsSuccess && !string.IsNullOrEmpty(r.ErrorCode))
                    .GroupBy(r => r.ErrorCode!)
                    .Select(g => new { ErrorCode = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(10));

            return new LogStatistics
            {
                TotalLogs = totalLogs,
                SuccessfulLogs = successfulLogs,
                FailedLogs = failedLogs,
                SuccessRate = totalLogs > 0 ? (double)successfulLogs / totalLogs * 100 : 0,
                AverageResponseTime = avgResponseTime,
                TotalCost = totalCost,
                TotalTokens = totalTokens,
                TopModels = topModels.Select(x => new ModelLogCount { ModelName = x.Model, Count = x.Count }).ToList(),
                TopErrors = topErrors.Select(x => new ErrorLogCount { ErrorCode = x.ErrorCode, Count = x.Count }).ToList(),
                StatisticsPeriod = new DateRange(start, end)
            };
        }

        /// <summary>
        /// 搜索日志
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<RequestLogDto>> SearchLogsAsync(SearchLogsDto input)
        {
            var queryable = await _requestLogRepository.GetQueryableAsync();
            var query = queryable.AsQueryable();

            if (!string.IsNullOrEmpty(input.SearchTerm))
            {
                query = query.Where(r =>
                    r.RequestPath.Contains(input.SearchTerm) ||
                    (r.ModelName != null && r.ModelName.Contains(input.SearchTerm)) ||
                    (r.RequestIp != null && r.RequestIp.Contains(input.SearchTerm)) ||
                    (r.ErrorCode != null && r.ErrorCode.Contains(input.SearchTerm)) ||
                    (r.ErrorMessage != null && r.ErrorMessage.Contains(input.SearchTerm)));
            }

            if (input.StartDate.HasValue)
                query = query.Where(r => r.CreationTime >= input.StartDate.Value);

            if (input.EndDate.HasValue)
                query = query.Where(r => r.CreationTime <= input.EndDate.Value);

            query = query.OrderByDescending(r => r.CreationTime);
            var logs = await AsyncExecuter.ToListAsync(
                query.Take(input.MaxResults));

            return ObjectMapper.Map<List<RequestLog>, List<RequestLogDto>>(logs);
        }
    }

    // DTOs
    public class GetRequestLogsDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Guid? UserId { get; set; }
        public Guid? TokenId { get; set; }
        public string? ModelName { get; set; }
        public string? Endpoint { get; set; }
        public bool? IsSuccess { get; set; }
        public double? MinResponseTime { get; set; }
        public double? MaxResponseTime { get; set; }
        public string? ErrorCode { get; set; }
        public string? IpAddress { get; set; }
        public string? SortBy { get; set; } = "CreationTime";
        public string SortDirection { get; set; } = "desc";
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 50;
    }

    public class GetMyRequestLogsDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ModelName { get; set; }
        public bool? IsSuccess { get; set; }
        public int SkipCount { get; set; } = 0;
        public int MaxResultCount { get; set; } = 50;
    }

    public class ExportRequestLogsDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Guid? UserId { get; set; }
        public string? ModelName { get; set; }
        public bool? IsSuccess { get; set; }
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
    }

    public class SearchLogsDto
    {
        [Required]
        public string SearchTerm { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int MaxResults { get; set; } = 100;
    }



    public class RequestLogDetailDto : RequestLogDto
    {
        public new string? RequestHeaders { get; set; }
        public new string? ResponseHeaders { get; set; }
        public string? ResponseContent { get; set; }
    }

    public class RequestLogExportDto
    {
        public Guid Id { get; set; }
        public DateTime CreationTime { get; set; }
        public string Endpoint { get; set; } = string.Empty;
        public string ModelName { get; set; } = string.Empty;
        public Guid UserId { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public double? ResponseTime { get; set; }
        public long? InputTokens { get; set; }
        public long? OutputTokens { get; set; }
        public decimal? Cost { get; set; }
        public string? ErrorCode { get; set; }
    }

    public class LogExportResult
    {
        public Guid ExportId { get; set; }
        public DateTime ExportedAt { get; set; }
        public int TotalRecords { get; set; }
        public List<RequestLogExportDto> Data { get; set; } = new();
        public ExportFormat Format { get; set; }
        public string FileName { get; set; } = string.Empty;
    }

    public class LogStatistics
    {
        public int TotalLogs { get; set; }
        public int SuccessfulLogs { get; set; }
        public int FailedLogs { get; set; }
        public double SuccessRate { get; set; }
        public double AverageResponseTime { get; set; }
        public decimal TotalCost { get; set; }
        public long TotalTokens { get; set; }
        public List<ModelLogCount> TopModels { get; set; } = new();
        public List<ErrorLogCount> TopErrors { get; set; } = new();
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public class ModelLogCount
    {
        public string ModelName { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class ErrorLogCount
    {
        public string ErrorCode { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public enum ExportFormat
    {
        CSV = 1,
        JSON = 2,
        Excel = 3
    }
}
