using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 渠道实体 - AI服务提供商渠道聚合根
    /// </summary>
    public class Channel : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// 渠道名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 渠道类型
        /// </summary>
        public ChannelType Type { get; set; }

        /// <summary>
        /// 渠道状态
        /// </summary>
        public ChannelStatus Status { get; set; } = ChannelStatus.Enabled;

        /// <summary>
        /// API基础URL
        /// </summary>
        [Required]
        [StringLength(500)]
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// API密钥
        /// </summary>
        [Required]
        [StringLength(500)]
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 组织ID (某些API需要)
        /// </summary>
        [StringLength(100)]
        public string? OrganizationId { get; set; }

        /// <summary>
        /// 项目ID (某些API需要)
        /// </summary>
        [StringLength(100)]
        public string? ProjectId { get; set; }

        /// <summary>
        /// 优先级 (数值越小优先级越高)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 权重 (用于负载均衡)
        /// </summary>
        public int Weight { get; set; } = 1;

        /// <summary>
        /// 支持的模型列表 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? SupportedModels { get; set; }

        /// <summary>
        /// 模型映射配置 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? ModelMapping { get; set; }

        /// <summary>
        /// 自定义配置 (JSON格式)
        /// </summary>
        [StringLength(2000)]
        public string? Config { get; set; }

        /// <summary>
        /// 分组标签
        /// </summary>
        [StringLength(100)]
        public string? Group { get; set; }

        /// <summary>
        /// 最大并发数
        /// </summary>
        public int MaxConcurrency { get; set; } = 10;

        /// <summary>
        /// 请求超时时间 (秒)
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; } = 0;

        /// <summary>
        /// 余额更新时间
        /// </summary>
        public DateTime? BalanceUpdatedAt { get; set; }

        /// <summary>
        /// 是否自动检查余额
        /// </summary>
        public bool AutoCheckBalance { get; set; } = true;

        /// <summary>
        /// 余额检查间隔 (分钟)
        /// </summary>
        public int BalanceCheckInterval { get; set; } = 60;

        /// <summary>
        /// 上次使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 上次检查时间
        /// </summary>
        public DateTime? LastCheckedTime { get; set; }

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests { get; set; } = 0;

        /// <summary>
        /// 成功请求次数
        /// </summary>
        public long SuccessfulRequests { get; set; } = 0;

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 最大失败次数 (超过后自动禁用)
        /// </summary>
        public int MaxFailures { get; set; } = 10;

        /// <summary>
        /// 平均响应时间 (毫秒)
        /// </summary>
        public double AverageResponseTime { get; set; } = 0;

        /// <summary>
        /// 最后错误信息
        /// </summary>
        [StringLength(1000)]
        public string? LastError { get; set; }

        /// <summary>
        /// 最后错误时间
        /// </summary>
        public DateTime? LastErrorTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public Channel()
        {
        }

        public Channel(Guid id, string name, ChannelType type, string baseUrl, string apiKey)
        {
            Id = id;
            Name = name;
            Type = type;
            BaseUrl = baseUrl;
            ApiKey = apiKey;
            Status = ChannelStatus.Enabled;
            Priority = 0;
            Weight = 1;
            MaxConcurrency = 10;
            TimeoutSeconds = 30;
            RetryCount = 3;
            Balance = 0;
            AutoCheckBalance = true;
            BalanceCheckInterval = 60;
            TotalRequests = 0;
            SuccessfulRequests = 0;
            FailureCount = 0;
            MaxFailures = 10;
            AverageResponseTime = 0;
        }

        /// <summary>
        /// 记录成功请求
        /// </summary>
        public void RecordSuccessfulRequest(double responseTime)
        {
            LastUsedTime = DateTime.UtcNow;
            TotalRequests++;
            SuccessfulRequests++;
            
            // 更新平均响应时间
            if (SuccessfulRequests == 1)
            {
                AverageResponseTime = responseTime;
            }
            else
            {
                AverageResponseTime = (AverageResponseTime * (SuccessfulRequests - 1) + responseTime) / SuccessfulRequests;
            }
            
            // 重置失败次数
            FailureCount = 0;
            
            // 如果之前被禁用，重新启用
            if (Status == ChannelStatus.DisabledByFailure)
            {
                Status = ChannelStatus.Enabled;
            }
        }

        /// <summary>
        /// 记录失败请求
        /// </summary>
        public void RecordFailedRequest(string? errorMessage = null)
        {
            TotalRequests++;
            FailureCount++;
            
            if (!string.IsNullOrEmpty(errorMessage))
            {
                LastError = errorMessage;
                LastErrorTime = DateTime.UtcNow;
            }
            
            // 如果失败次数超过最大值，自动禁用
            if (FailureCount >= MaxFailures && Status == ChannelStatus.Enabled)
            {
                Status = ChannelStatus.DisabledByFailure;
            }
        }

        /// <summary>
        /// 重置失败计数
        /// </summary>
        public void ResetFailureCount()
        {
            FailureCount = 0;
            if (Status == ChannelStatus.DisabledByFailure)
            {
                Status = ChannelStatus.Enabled;
            }
        }

        /// <summary>
        /// 记录健康检查
        /// </summary>
        public void RecordHealthCheck(bool isHealthy, string? errorMessage = null)
        {
            LastCheckedTime = DateTime.UtcNow;
            
            if (isHealthy)
            {
                ResetFailureCount();
            }
            else
            {
                RecordFailedRequest(errorMessage);
            }
        }

        /// <summary>
        /// 更新余额
        /// </summary>
        public void UpdateBalance(decimal newBalance)
        {
            Balance = newBalance;
            BalanceUpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 检查是否可用
        /// </summary>
        public bool IsAvailable()
        {
            return Status == ChannelStatus.Enabled && Balance > 0;
        }

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double GetSuccessRate()
        {
            if (TotalRequests == 0)
                return 0;

            return (double)SuccessfulRequests / TotalRequests * 100;
        }

        /// <summary>
        /// 启用渠道
        /// </summary>
        public void Enable()
        {
            Status = ChannelStatus.Enabled;
            FailureCount = 0;
        }

        /// <summary>
        /// 禁用渠道
        /// </summary>
        public void Disable()
        {
            Status = ChannelStatus.Disabled;
        }
    }

    /// <summary>
    /// 渠道类型枚举
    /// </summary>
    public enum ChannelType
    {
        OpenAI = 1,
        Claude = 2,
        Gemini = 3,
        AzureOpenAI = 4,
        BaiduWenxin = 5,
        XunfeiSpark = 6,
        ZhipuGLM = 7,
        MoonshotAI = 8,
        DeepSeek = 9,
        AliTongyi = 10,
        TencentHunyuan = 11,
        ByteDance = 12,
        Custom = 99
    }

    /// <summary>
    /// 渠道状态枚举
    /// </summary>
    public enum ChannelStatus
    {
        /// <summary>
        /// 启用
        /// </summary>
        Enabled = 1,

        /// <summary>
        /// 禁用
        /// </summary>
        Disabled = 2,

        /// <summary>
        /// 因失败自动禁用
        /// </summary>
        DisabledByFailure = 3,

        /// <summary>
        /// 余额不足
        /// </summary>
        InsufficientBalance = 4
    }
}
