using myaiservice.Entities;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace myaiservice.Data
{
    /// <summary>
    /// 模型数据种子贡献者
    /// </summary>
    public class ModelDataSeedContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Model, Guid> _modelRepository;

        public ModelDataSeedContributor(IRepository<Model, Guid> modelRepository)
        {
            _modelRepository = modelRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _modelRepository.GetCountAsync() > 0)
            {
                return; // 已有数据，跳过种子数据
            }

            var models = new List<Model>
            {
                // OpenAI 模型
                new Model(Guid.NewGuid(), "gpt-4", "GPT-4", ModelType.Chat, ModelProvider.OpenAI)
                {
                    InputPrice = 0.03m,
                    OutputPrice = 0.06m,
                    MaxContextLength = 8192,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    Description = "OpenAI's most capable model",
                    Version = "gpt-4-0613",
                    SortOrder = 1
                },
                new Model(Guid.NewGuid(), "gpt-4-turbo", "GPT-4 Turbo", ModelType.Chat, ModelProvider.OpenAI)
                {
                    InputPrice = 0.01m,
                    OutputPrice = 0.03m,
                    MaxContextLength = 128000,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    SupportsVision = true,
                    Description = "GPT-4 Turbo with vision capabilities",
                    Version = "gpt-4-turbo-2024-04-09",
                    SortOrder = 2
                },
                new Model(Guid.NewGuid(), "gpt-3.5-turbo", "GPT-3.5 Turbo", ModelType.Chat, ModelProvider.OpenAI)
                {
                    InputPrice = 0.0015m,
                    OutputPrice = 0.002m,
                    MaxContextLength = 16385,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    Description = "Fast and efficient model for most tasks",
                    Version = "gpt-3.5-turbo-0125",
                    SortOrder = 3
                },
                new Model(Guid.NewGuid(), "text-embedding-3-large", "Text Embedding 3 Large", ModelType.Embedding, ModelProvider.OpenAI)
                {
                    InputPrice = 0.00013m,
                    OutputPrice = 0m,
                    MaxContextLength = 8191,
                    MaxOutputLength = 3072,
                    Description = "Most capable embedding model",
                    SortOrder = 4
                },
                new Model(Guid.NewGuid(), "dall-e-3", "DALL-E 3", ModelType.ImageGeneration, ModelProvider.OpenAI)
                {
                    InputPrice = 0.04m, // per image
                    OutputPrice = 0m,
                    SupportsImageGeneration = true,
                    Description = "Advanced image generation model",
                    SortOrder = 5
                },

                // Anthropic Claude 模型
                new Model(Guid.NewGuid(), "claude-3-opus-20240229", "Claude 3 Opus", ModelType.Chat, ModelProvider.Anthropic)
                {
                    InputPrice = 0.015m,
                    OutputPrice = 0.075m,
                    MaxContextLength = 200000,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsVision = true,
                    Description = "Anthropic's most powerful model",
                    Version = "claude-3-opus-20240229",
                    SortOrder = 6
                },
                new Model(Guid.NewGuid(), "claude-3-sonnet-20240229", "Claude 3 Sonnet", ModelType.Chat, ModelProvider.Anthropic)
                {
                    InputPrice = 0.003m,
                    OutputPrice = 0.015m,
                    MaxContextLength = 200000,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsVision = true,
                    Description = "Balanced performance and speed",
                    Version = "claude-3-sonnet-20240229",
                    SortOrder = 7
                },
                new Model(Guid.NewGuid(), "claude-3-haiku-20240307", "Claude 3 Haiku", ModelType.Chat, ModelProvider.Anthropic)
                {
                    InputPrice = 0.00025m,
                    OutputPrice = 0.00125m,
                    MaxContextLength = 200000,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsVision = true,
                    Description = "Fast and cost-effective model",
                    Version = "claude-3-haiku-20240307",
                    SortOrder = 8
                },

                // Google Gemini 模型
                new Model(Guid.NewGuid(), "gemini-pro", "Gemini Pro", ModelType.Chat, ModelProvider.Google)
                {
                    InputPrice = 0.0005m,
                    OutputPrice = 0.0015m,
                    MaxContextLength = 30720,
                    MaxOutputLength = 2048,
                    SupportsStreaming = true,
                    Description = "Google's advanced language model",
                    SortOrder = 9
                },
                new Model(Guid.NewGuid(), "gemini-pro-vision", "Gemini Pro Vision", ModelType.Multimodal, ModelProvider.Google)
                {
                    InputPrice = 0.0005m,
                    OutputPrice = 0.0015m,
                    MaxContextLength = 30720,
                    MaxOutputLength = 2048,
                    SupportsStreaming = true,
                    SupportsVision = true,
                    Description = "Gemini Pro with vision capabilities",
                    SortOrder = 10
                },

                // 国产模型
                new Model(Guid.NewGuid(), "ernie-bot-turbo", "文心一言 Turbo", ModelType.Chat, ModelProvider.Baidu)
                {
                    InputPrice = 0.008m,
                    OutputPrice = 0.008m,
                    MaxContextLength = 8192,
                    MaxOutputLength = 2048,
                    SupportsStreaming = true,
                    Description = "百度文心一言模型",
                    SortOrder = 11
                },
                new Model(Guid.NewGuid(), "spark-3.0", "讯飞星火 3.0", ModelType.Chat, ModelProvider.Xunfei)
                {
                    InputPrice = 0.003m,
                    OutputPrice = 0.003m,
                    MaxContextLength = 8192,
                    MaxOutputLength = 2048,
                    SupportsStreaming = true,
                    Description = "科大讯飞星火认知大模型",
                    SortOrder = 12
                },
                new Model(Guid.NewGuid(), "glm-4", "智谱GLM-4", ModelType.Chat, ModelProvider.Zhipu)
                {
                    InputPrice = 0.1m,
                    OutputPrice = 0.1m,
                    MaxContextLength = 128000,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    Description = "智谱AI的GLM-4模型",
                    SortOrder = 13
                },
                new Model(Guid.NewGuid(), "moonshot-v1-8k", "Moonshot v1 8K", ModelType.Chat, ModelProvider.Moonshot)
                {
                    InputPrice = 0.012m,
                    OutputPrice = 0.012m,
                    MaxContextLength = 8192,
                    MaxOutputLength = 2048,
                    SupportsStreaming = true,
                    Description = "月之暗面Kimi模型",
                    SortOrder = 14
                },
                new Model(Guid.NewGuid(), "deepseek-chat", "DeepSeek Chat", ModelType.Chat, ModelProvider.DeepSeek)
                {
                    InputPrice = 0.0014m,
                    OutputPrice = 0.0028m,
                    MaxContextLength = 32768,
                    MaxOutputLength = 4096,
                    SupportsStreaming = true,
                    SupportsFunctionCalling = true,
                    Description = "DeepSeek深度求索模型",
                    SortOrder = 15
                }
            };

            await _modelRepository.InsertManyAsync(models);
        }
    }
}
