﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.EntityFrameworkCore.Sqlite;
using Microsoft.Extensions.Configuration;

namespace myaiservice.Data;

public class myaiserviceDbContextFactory : IDesignTimeDbContextFactory<myaiserviceDbContext>
{
    public myaiserviceDbContext CreateDbContext(string[] args)
    {
        myaiserviceEfCoreEntityExtensionMappings.Configure();
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<myaiserviceDbContext>()
            .UseSqlite(configuration.GetConnectionString("Default"));

        return new myaiserviceDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}