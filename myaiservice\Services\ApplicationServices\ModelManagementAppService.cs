using myaiservice.Entities;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// 模型管理应用服务
    /// </summary>
    public class ModelManagementAppService : ApplicationService
    {
        private readonly IRepository<Model, Guid> _modelRepository;

        public ModelManagementAppService(IRepository<Model, Guid> modelRepository)
        {
            _modelRepository = modelRepository;
        }

        /// <summary>
        /// 获取所有模型
        /// </summary>
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsAsync()
        {
            var models = await _modelRepository.GetListAsync();
            return models
                .Where(m => m.IsEnabled && !m.IsDeprecated)
                .OrderBy(m => m.SortOrder)
                .ThenBy(m => m.Name)
                .Select(m => ObjectMapper.Map<Model, ModelDto>(m))
                .ToList();
        }

        /// <summary>
        /// 获取模型详情
        /// </summary>
        public async Task<ModelDto> GetModelAsync(Guid id)
        {
            var model = await _modelRepository.GetAsync(id);
            return ObjectMapper.Map<Model, ModelDto>(model);
        }

        /// <summary>
        /// 按名称获取模型
        /// </summary>
        [AllowAnonymous]
        public async Task<ModelDto?> GetModelByNameAsync(string name)
        {
            var model = await _modelRepository.FirstOrDefaultAsync(m => m.Name == name);
            return model != null ? ObjectMapper.Map<Model, ModelDto>(model) : null;
        }

        /// <summary>
        /// 创建模型
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<ModelDto> CreateModelAsync(CreateModelDto input)
        {
            // 检查模型名称是否已存在
            var existingModel = await _modelRepository.FirstOrDefaultAsync(m => m.Name == input.Name);
            if (existingModel != null)
                throw new InvalidOperationException($"Model with name '{input.Name}' already exists");

            var model = new Model(GuidGenerator.Create(), input.Name, input.DisplayName, input.Type, input.Provider)
            {
                InputPrice = input.InputPrice,
                OutputPrice = input.OutputPrice,
                CachePrice = input.CachePrice,
                MaxContextLength = input.MaxContextLength,
                MaxOutputLength = input.MaxOutputLength,
                SupportsStreaming = input.SupportsStreaming,
                SupportsFunctionCalling = input.SupportsFunctionCalling,
                SupportsVision = input.SupportsVision,
                SupportsAudio = input.SupportsAudio,
                SupportsImageGeneration = input.SupportsImageGeneration,
                SupportedFileTypes = input.SupportedFileTypes,
                Description = input.Description,
                Version = input.Version,
                ReleaseDate = input.ReleaseDate,
                SortOrder = input.SortOrder,
                Remarks = input.Remarks
            };

            await _modelRepository.InsertAsync(model);

            return ObjectMapper.Map<Model, ModelDto>(model);
        }

        /// <summary>
        /// 更新模型
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<ModelDto> UpdateModelAsync(Guid id, UpdateModelDto input)
        {
            var model = await _modelRepository.GetAsync(id);

            model.DisplayName = input.DisplayName ?? model.DisplayName;
            model.IsEnabled = input.IsEnabled ?? model.IsEnabled;
            model.InputPrice = input.InputPrice ?? model.InputPrice;
            model.OutputPrice = input.OutputPrice ?? model.OutputPrice;
            model.CachePrice = input.CachePrice ?? model.CachePrice;
            model.MaxContextLength = input.MaxContextLength ?? model.MaxContextLength;
            model.MaxOutputLength = input.MaxOutputLength ?? model.MaxOutputLength;
            model.SupportsStreaming = input.SupportsStreaming ?? model.SupportsStreaming;
            model.SupportsFunctionCalling = input.SupportsFunctionCalling ?? model.SupportsFunctionCalling;
            model.SupportsVision = input.SupportsVision ?? model.SupportsVision;
            model.SupportsAudio = input.SupportsAudio ?? model.SupportsAudio;
            model.SupportsImageGeneration = input.SupportsImageGeneration ?? model.SupportsImageGeneration;
            model.SupportedFileTypes = input.SupportedFileTypes ?? model.SupportedFileTypes;
            model.Description = input.Description ?? model.Description;
            model.Version = input.Version ?? model.Version;
            model.SortOrder = input.SortOrder ?? model.SortOrder;
            model.Remarks = input.Remarks ?? model.Remarks;

            await _modelRepository.UpdateAsync(model);

            return ObjectMapper.Map<Model, ModelDto>(model);
        }

        /// <summary>
        /// 删除模型
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task DeleteModelAsync(Guid id)
        {
            await _modelRepository.DeleteAsync(id);
        }

        /// <summary>
        /// 启用/禁用模型
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task SetModelStatusAsync(Guid id, bool isEnabled)
        {
            var model = await _modelRepository.GetAsync(id);
            model.IsEnabled = isEnabled;
            await _modelRepository.UpdateAsync(model);
        }

        /// <summary>
        /// 标记模型为已弃用
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task DeprecateModelAsync(Guid id, string? replacementModel = null)
        {
            var model = await _modelRepository.GetAsync(id);
            model.MarkAsDeprecated(replacementModel);
            await _modelRepository.UpdateAsync(model);
        }

        /// <summary>
        /// 获取模型统计信息
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<ModelStatisticsDto> GetModelStatisticsAsync(Guid id)
        {
            var model = await _modelRepository.GetAsync(id);
            
            return new ModelStatisticsDto
            {
                ModelId = id,
                Name = model.Name,
                DisplayName = model.DisplayName,
                TotalRequests = model.TotalRequests,
                SuccessfulRequests = model.SuccessfulRequests,
                SuccessRate = model.GetSuccessRate(),
                TotalTokens = model.TotalTokens,
                InputTokens = model.InputTokens,
                OutputTokens = model.OutputTokens,
                TotalCost = model.TotalCost,
                AverageResponseTime = model.AverageResponseTime,
                AverageCostPerRequest = model.GetAverageCostPerRequest()
            };
        }

        /// <summary>
        /// 获取模型使用排行
        /// </summary>
        [Authorize(Roles = "admin")]
        public async Task<List<ModelUsageDto>> GetModelUsageRankingAsync(int count = 10)
        {
            var models = await _modelRepository.GetListAsync();
            
            return models
                .OrderByDescending(m => m.TotalRequests)
                .Take(count)
                .Select(m => new ModelUsageDto
                {
                    ModelId = m.Id,
                    ModelName = m.Name,
                    RequestCount = m.TotalRequests,
                    TokenCount = m.TotalTokens,
                    Cost = m.TotalCost,
                    SuccessRate = m.GetSuccessRate()
                })
                .ToList();
        }

        /// <summary>
        /// 按类型获取模型
        /// </summary>
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsByTypeAsync(ModelType type)
        {
            var models = await _modelRepository.GetListAsync(m => m.Type == type && m.IsEnabled && !m.IsDeprecated);
            
            return models
                .OrderBy(m => m.SortOrder)
                .ThenBy(m => m.Name)
                .Select(m => ObjectMapper.Map<Model, ModelDto>(m))
                .ToList();
        }

        /// <summary>
        /// 按提供商获取模型
        /// </summary>
        [AllowAnonymous]
        public async Task<List<ModelDto>> GetModelsByProviderAsync(ModelProvider provider)
        {
            var models = await _modelRepository.GetListAsync(m => m.Provider == provider && m.IsEnabled && !m.IsDeprecated);
            
            return models
                .OrderBy(m => m.SortOrder)
                .ThenBy(m => m.Name)
                .Select(m => ObjectMapper.Map<Model, ModelDto>(m))
                .ToList();
        }
    }

    // DTOs
    public class UpdateModelDto
    {
        public string? DisplayName { get; set; }
        public bool? IsEnabled { get; set; }
        public decimal? InputPrice { get; set; }
        public decimal? OutputPrice { get; set; }
        public decimal? CachePrice { get; set; }
        public int? MaxContextLength { get; set; }
        public int? MaxOutputLength { get; set; }
        public bool? SupportsStreaming { get; set; }
        public bool? SupportsFunctionCalling { get; set; }
        public bool? SupportsVision { get; set; }
        public bool? SupportsAudio { get; set; }
        public bool? SupportsImageGeneration { get; set; }
        public string? SupportedFileTypes { get; set; }
        public string? Description { get; set; }
        public string? Version { get; set; }
        public int? SortOrder { get; set; }
        public string? Remarks { get; set; }
    }

    public class ModelStatisticsDto
    {
        public Guid ModelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public long TotalTokens { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public decimal TotalCost { get; set; }
        public double AverageResponseTime { get; set; }
        public decimal AverageCostPerRequest { get; set; }
    }
}
