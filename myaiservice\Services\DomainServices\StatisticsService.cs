using myaiservice.Entities;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using System.Text.Json;

namespace myaiservice.Services.DomainServices
{
    /// <summary>
    /// 统计分析服务 - 处理各种数据统计和分析
    /// </summary>
    public class StatisticsService : DomainService
    {
        private readonly IRepository<RequestLog, Guid> _requestLogRepository;
        private readonly IRepository<Token, Guid> _tokenRepository;
        private readonly IRepository<Channel, Guid> _channelRepository;
        private readonly IRepository<Model, Guid> _modelRepository;
        private readonly IRepository<QuotaTransaction, Guid> _quotaTransactionRepository;

        public StatisticsService(
            IRepository<RequestLog, Guid> requestLogRepository,
            IRepository<Token, Guid> tokenRepository,
            IRepository<Channel, Guid> channelRepository,
            IRepository<Model, Guid> modelRepository,
            IRepository<QuotaTransaction, Guid> quotaTransactionRepository)
        {
            _requestLogRepository = requestLogRepository;
            _tokenRepository = tokenRepository;
            _channelRepository = channelRepository;
            _modelRepository = modelRepository;
            _quotaTransactionRepository = quotaTransactionRepository;
        }

        /// <summary>
        /// 获取系统总览统计
        /// </summary>
        public async Task<SystemOverviewStatistics> GetSystemOverviewAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var totalRequests = await AsyncExecuter.CountAsync(requests);
            var successfulRequests = await AsyncExecuter.CountAsync(requests.Where(r => r.IsSuccess));
            var totalTokens = await AsyncExecuter.SumAsync(requests, r => r.InputTokens + r.OutputTokens);
            var totalCost = await AsyncExecuter.SumAsync(requests, r => r.Cost);

            var transactionQueryable = await _quotaTransactionRepository.GetQueryableAsync();
            var transactions = transactionQueryable.Where(t => t.CreationTime >= start && t.CreationTime < end);
            var totalRevenue = await AsyncExecuter.SumAsync(transactions.Where(t => t.Type == QuotaTransactionType.Recharge), t => t.Amount);

            var activeUsers = await AsyncExecuter.CountAsync(requests.Select(r => r.UserId).Distinct());
            var activeTokens = await AsyncExecuter.CountAsync(requests.Where(r => r.TokenId.HasValue).Select(r => r.TokenId!.Value).Distinct());

            return new SystemOverviewStatistics
            {
                TotalRequests = totalRequests,
                SuccessfulRequests = successfulRequests,
                FailedRequests = totalRequests - successfulRequests,
                SuccessRate = totalRequests > 0 ? (double)successfulRequests / totalRequests * 100 : 0,
                TotalTokens = totalTokens,
                TotalCost = totalCost,
                TotalRevenue = totalRevenue,
                ActiveUsers = activeUsers,
                ActiveTokens = activeTokens,
                AverageResponseTime = await GetAverageResponseTimeAsync(start, end),
                StatisticsPeriod = new DateRange(start, end)
            };
        }

        /// <summary>
        /// 获取请求趋势统计
        /// </summary>
        public async Task<List<RequestTrendStatistics>> GetRequestTrendAsync(DateTime startDate, DateTime endDate, TrendInterval interval = TrendInterval.Daily)
        {
            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= startDate && r.CreationTime < endDate);

            var groupedData = interval switch
            {
                TrendInterval.Hourly => await AsyncExecuter.ToListAsync(
                    requests.GroupBy(r => new { r.CreationTime.Date, r.CreationTime.Hour })
                            .Select(g => new RequestTrendStatistics
                            {
                                Period = g.Key.Date.AddHours(g.Key.Hour),
                                TotalRequests = g.Count(),
                                SuccessfulRequests = g.Count(r => r.IsSuccess),
                                FailedRequests = g.Count(r => !r.IsSuccess),
                                TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                                TotalCost = g.Sum(r => r.Cost),
                                AverageResponseTime = g.Average(r => r.ResponseTime)
                            })),
                TrendInterval.Daily => await AsyncExecuter.ToListAsync(
                    requests.GroupBy(r => r.CreationTime.Date)
                            .Select(g => new RequestTrendStatistics
                            {
                                Period = g.Key,
                                TotalRequests = g.Count(),
                                SuccessfulRequests = g.Count(r => r.IsSuccess),
                                FailedRequests = g.Count(r => !r.IsSuccess),
                                TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                                TotalCost = g.Sum(r => r.Cost),
                                AverageResponseTime = g.Average(r => r.ResponseTime)
                            })),
                TrendInterval.Weekly => await GetWeeklyTrendAsync(requests),
                TrendInterval.Monthly => await GetMonthlyTrendAsync(requests),
                _ => new List<RequestTrendStatistics>()
            };

            return groupedData.OrderBy(d => d.Period).ToList();
        }

        /// <summary>
        /// 获取模型使用统计
        /// </summary>
        public async Task<List<ModelUsageStatistics>> GetModelUsageStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var modelStats = await AsyncExecuter.ToListAsync(
                requests.GroupBy(r => r.ModelName)
                        .Select(g => new ModelUsageStatistics
                        {
                            ModelName = g.Key,
                            TotalRequests = g.Count(),
                            SuccessfulRequests = g.Count(r => r.IsSuccess),
                            FailedRequests = g.Count(r => !r.IsSuccess),
                            TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                            TotalCost = g.Sum(r => r.Cost),
                            AverageResponseTime = g.Average(r => r.ResponseTime),
                            UniqueUsers = g.Select(r => r.UserId).Distinct().Count()
                        }));

            return modelStats.OrderByDescending(s => s.TotalRequests).ToList();
        }

        /// <summary>
        /// 获取渠道性能统计
        /// </summary>
        public async Task<List<ChannelPerformanceStatistics>> GetChannelPerformanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end && r.ChannelId.HasValue);

            var channelStats = await AsyncExecuter.ToListAsync(
                requests.GroupBy(r => r.ChannelId!.Value)
                        .Select(g => new ChannelPerformanceStatistics
                        {
                            ChannelId = g.Key,
                            TotalRequests = g.Count(),
                            SuccessfulRequests = g.Count(r => r.IsSuccess),
                            FailedRequests = g.Count(r => !r.IsSuccess),
                            AverageResponseTime = g.Average(r => r.ResponseTime),
                            TotalCost = g.Sum(r => r.Cost),
                            ErrorRate = (double)g.Count(r => !r.IsSuccess) / g.Count() * 100
                        }));

            // 获取渠道信息
            var channels = await _channelRepository.GetListAsync();
            foreach (var stat in channelStats)
            {
                var channel = channels.FirstOrDefault(c => c.Id == stat.ChannelId);
                if (channel != null)
                {
                    stat.ChannelName = channel.Name;
                    stat.ChannelType = channel.Type;
                }
            }

            return channelStats.OrderByDescending(s => s.TotalRequests).ToList();
        }

        /// <summary>
        /// 获取用户活跃度统计
        /// </summary>
        public async Task<List<UserActivityStatistics>> GetUserActivityStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int topCount = 50)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var userStats = await AsyncExecuter.ToListAsync(
                requests.GroupBy(r => r.UserId)
                        .Select(g => new UserActivityStatistics
                        {
                            UserId = g.Key ?? Guid.Empty,
                            TotalRequests = g.Count(),
                            SuccessfulRequests = g.Count(r => r.IsSuccess),
                            TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                            TotalCost = g.Sum(r => r.Cost),
                            LastRequestTime = g.Max(r => r.CreationTime),
                            UniqueModels = g.Select(r => r.ModelName).Distinct().Count(),
                            AverageResponseTime = g.Average(r => r.ResponseTime)
                        }));

            return userStats.OrderByDescending(s => s.TotalRequests).Take(topCount).ToList();
        }

        /// <summary>
        /// 获取错误分析统计
        /// </summary>
        public async Task<List<ErrorAnalysisStatistics>> GetErrorAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var failedRequests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end && !r.IsSuccess);

            var errorStats = await AsyncExecuter.ToListAsync(
                failedRequests.GroupBy(r => new { r.ResponseStatusCode, r.ErrorCode })
                             .Select(g => new ErrorAnalysisStatistics
                             {
                                 StatusCode = g.Key.ResponseStatusCode,
                                 ErrorCode = g.Key.ErrorCode ?? "UNKNOWN",
                                 Count = g.Count(),
                                 Percentage = 0, // 将在后面计算
                                 FirstOccurrence = g.Min(r => r.CreationTime),
                                 LastOccurrence = g.Max(r => r.CreationTime),
                                 AffectedModels = g.Select(r => r.ModelName).Distinct().Count(),
                                 AffectedUsers = g.Select(r => r.UserId).Distinct().Count()
                             }));

            var totalErrors = errorStats.Sum(s => s.Count);
            foreach (var stat in errorStats)
            {
                stat.Percentage = totalErrors > 0 ? (double)stat.Count / totalErrors * 100 : 0;
            }

            return errorStats.OrderByDescending(s => s.Count).ToList();
        }

        /// <summary>
        /// 获取实时监控数据
        /// </summary>
        public async Task<RealTimeMonitoringData> GetRealTimeMonitoringDataAsync()
        {
            var now = DateTime.UtcNow;
            var last5Minutes = now.AddMinutes(-5);
            var lastHour = now.AddHours(-1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            
            var recent5MinRequests = requestQueryable.Where(r => r.CreationTime >= last5Minutes);
            var lastHourRequests = requestQueryable.Where(r => r.CreationTime >= lastHour);

            var currentRPS = await AsyncExecuter.CountAsync(recent5MinRequests) / 5.0 / 60.0; // 每秒请求数
            var currentSuccessRate = await GetSuccessRateAsync(recent5MinRequests);
            var currentAvgResponseTime = await GetAverageResponseTimeAsync(last5Minutes, now);

            var hourlyTrend = await AsyncExecuter.ToListAsync(
                lastHourRequests.GroupBy(r => new { r.CreationTime.Hour, Minute = r.CreationTime.Minute / 5 * 5 })
                               .Select(g => new RealTimeDataPoint
                               {
                                   Timestamp = now.Date.AddHours(g.Key.Hour).AddMinutes(g.Key.Minute),
                                   RequestCount = g.Count(),
                                   SuccessCount = g.Count(r => r.IsSuccess),
                                   AverageResponseTime = g.Average(r => r.ResponseTime)
                               }));

            return new RealTimeMonitoringData
            {
                CurrentRPS = currentRPS,
                CurrentSuccessRate = currentSuccessRate,
                CurrentAverageResponseTime = currentAvgResponseTime,
                ActiveConnections = await GetActiveConnectionsAsync(),
                SystemLoad = await GetSystemLoadAsync(),
                HourlyTrend = hourlyTrend.OrderBy(t => t.Timestamp).ToList(),
                LastUpdated = now
            };
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.Date.AddDays(-7);
            var end = endDate ?? DateTime.UtcNow.Date.AddDays(1);

            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var responseTimes = await AsyncExecuter.ToListAsync(requests.Select(r => r.ResponseTime));
            
            if (!responseTimes.Any())
            {
                return new PerformanceMetrics();
            }

            responseTimes.Sort();
            var count = responseTimes.Count;

            return new PerformanceMetrics
            {
                AverageResponseTime = responseTimes.Average(),
                MedianResponseTime = count % 2 == 0 
                    ? (responseTimes[count / 2 - 1] + responseTimes[count / 2]) / 2.0
                    : responseTimes[count / 2],
                P95ResponseTime = responseTimes[(int)(count * 0.95)],
                P99ResponseTime = responseTimes[(int)(count * 0.99)],
                MinResponseTime = responseTimes.Min(),
                MaxResponseTime = responseTimes.Max(),
                TotalRequests = count,
                StatisticsPeriod = new DateRange(start, end)
            };
        }

        // 私有辅助方法
        private async Task<List<RequestTrendStatistics>> GetWeeklyTrendAsync(IQueryable<RequestLog> requests)
        {
            // 实现周统计逻辑
            return await AsyncExecuter.ToListAsync(
                requests.GroupBy(r => new { Year = r.CreationTime.Year, Week = (r.CreationTime.DayOfYear - 1) / 7 })
                        .Select(g => new RequestTrendStatistics
                        {
                            Period = new DateTime(g.Key.Year, 1, 1).AddDays(g.Key.Week * 7),
                            TotalRequests = g.Count(),
                            SuccessfulRequests = g.Count(r => r.IsSuccess),
                            FailedRequests = g.Count(r => !r.IsSuccess),
                            TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                            TotalCost = g.Sum(r => r.Cost),
                            AverageResponseTime = g.Average(r => r.ResponseTime)
                        }));
        }

        private async Task<List<RequestTrendStatistics>> GetMonthlyTrendAsync(IQueryable<RequestLog> requests)
        {
            // 实现月统计逻辑
            return await AsyncExecuter.ToListAsync(
                requests.GroupBy(r => new { r.CreationTime.Year, r.CreationTime.Month })
                        .Select(g => new RequestTrendStatistics
                        {
                            Period = new DateTime(g.Key.Year, g.Key.Month, 1),
                            TotalRequests = g.Count(),
                            SuccessfulRequests = g.Count(r => r.IsSuccess),
                            FailedRequests = g.Count(r => !r.IsSuccess),
                            TotalTokens = g.Sum(r => r.InputTokens + r.OutputTokens),
                            TotalCost = g.Sum(r => r.Cost),
                            AverageResponseTime = g.Average(r => r.ResponseTime)
                        }));
        }

        private async Task<double> GetAverageResponseTimeAsync(DateTime start, DateTime end)
        {
            var requestQueryable = await _requestLogRepository.GetQueryableAsync();
            var requests = requestQueryable.Where(r => r.CreationTime >= start && r.CreationTime < end);

            var avgResponseTime = await AsyncExecuter.AverageAsync(requests, r => r.ResponseTime);
            return avgResponseTime;
        }

        private async Task<double> GetSuccessRateAsync(IQueryable<RequestLog> requests)
        {
            var total = await AsyncExecuter.CountAsync(requests);
            if (total == 0) return 100.0;
            
            var successful = await AsyncExecuter.CountAsync(requests.Where(r => r.IsSuccess));
            return (double)successful / total * 100;
        }

        private async Task<int> GetActiveConnectionsAsync()
        {
            // 简化实现，实际应该从连接池或监控系统获取
            return await Task.FromResult(0);
        }

        private async Task<double> GetSystemLoadAsync()
        {
            // 简化实现，实际应该从系统监控获取CPU使用率
            return await Task.FromResult(0.0);
        }
    }

    // 统计相关的数据传输对象
    public class SystemOverviewStatistics
    {
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long FailedRequests { get; set; }
        public double SuccessRate { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalRevenue { get; set; }
        public int ActiveUsers { get; set; }
        public int ActiveTokens { get; set; }
        public double AverageResponseTime { get; set; }
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public class RequestTrendStatistics
    {
        public DateTime Period { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public double AverageResponseTime { get; set; }
    }

    public class ModelUsageStatistics
    {
        public string ModelName { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public double AverageResponseTime { get; set; }
        public int UniqueUsers { get; set; }
    }

    public class ChannelPerformanceStatistics
    {
        public Guid ChannelId { get; set; }
        public string ChannelName { get; set; } = string.Empty;
        public ChannelType ChannelType { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public double AverageResponseTime { get; set; }
        public decimal TotalCost { get; set; }
        public double ErrorRate { get; set; }
    }

    public class UserActivityStatistics
    {
        public Guid UserId { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public long TotalTokens { get; set; }
        public decimal TotalCost { get; set; }
        public DateTime LastRequestTime { get; set; }
        public int UniqueModels { get; set; }
        public double AverageResponseTime { get; set; }
    }

    public class ErrorAnalysisStatistics
    {
        public int StatusCode { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
        public int Count { get; set; }
        public double Percentage { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public int AffectedModels { get; set; }
        public int AffectedUsers { get; set; }
    }

    public class RealTimeMonitoringData
    {
        public double CurrentRPS { get; set; }
        public double CurrentSuccessRate { get; set; }
        public double CurrentAverageResponseTime { get; set; }
        public int ActiveConnections { get; set; }
        public double SystemLoad { get; set; }
        public List<RealTimeDataPoint> HourlyTrend { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    public class RealTimeDataPoint
    {
        public DateTime Timestamp { get; set; }
        public int RequestCount { get; set; }
        public int SuccessCount { get; set; }
        public double AverageResponseTime { get; set; }
    }

    public class PerformanceMetrics
    {
        public double AverageResponseTime { get; set; }
        public double MedianResponseTime { get; set; }
        public double P95ResponseTime { get; set; }
        public double P99ResponseTime { get; set; }
        public double MinResponseTime { get; set; }
        public double MaxResponseTime { get; set; }
        public int TotalRequests { get; set; }
        public DateRange StatisticsPeriod { get; set; } = new();
    }

    public enum TrendInterval
    {
        Hourly = 1,
        Daily = 2,
        Weekly = 3,
        Monthly = 4
    }
}
