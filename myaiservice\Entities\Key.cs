﻿using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 原生API密钥实体
    /// </summary>
    public class Key : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// API秘钥名称
        /// </summary>
        public string? Name { get; set; }


        /// <summary>
        /// API秘钥类型
        /// </summary>
        public string? KeyType { get; set; }


        /// <summary>
        /// API秘钥值
        /// </summary>
        public List<string>? KeyValue { get; set; }
    }
}
