﻿using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace myaiservice.Entities
{
    /// <summary>
    /// 原生API密钥实体 - 用于存储第三方服务商的API密钥
    /// </summary>
    public class Key : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// API密钥名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// API密钥类型 (OpenAI, <PERSON>, Gemini等)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string KeyType { get; set; } = string.Empty;

        /// <summary>
        /// API密钥值列表 (支持多个密钥轮询)
        /// </summary>
        public List<string> KeyValues { get; set; } = new();

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 优先级 (数值越小优先级越高)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 权重 (用于负载均衡)
        /// </summary>
        public int Weight { get; set; } = 1;

        /// <summary>
        /// 最大并发数
        /// </summary>
        public int MaxConcurrency { get; set; } = 10;

        /// <summary>
        /// 当前使用的密钥索引
        /// </summary>
        public int CurrentKeyIndex { get; set; } = 0;

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 最大失败次数 (超过后自动禁用)
        /// </summary>
        public int MaxFailures { get; set; } = 5;

        /// <summary>
        /// 上次使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 上次检查时间
        /// </summary>
        public DateTime? LastCheckedTime { get; set; }

        /// <summary>
        /// 总调用次数
        /// </summary>
        public long TotalCalls { get; set; } = 0;

        /// <summary>
        /// 成功调用次数
        /// </summary>
        public long SuccessfulCalls { get; set; } = 0;

        /// <summary>
        /// 平均响应时间 (毫秒)
        /// </summary>
        public double AverageResponseTime { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        public Key()
        {
        }

        public Key(Guid id, string name, string keyType, List<string> keyValues)
        {
            Id = id;
            Name = name;
            KeyType = keyType;
            KeyValues = keyValues ?? new List<string>();
            IsEnabled = true;
            Priority = 0;
            Weight = 1;
            MaxConcurrency = 10;
            CurrentKeyIndex = 0;
            FailureCount = 0;
            MaxFailures = 5;
            TotalCalls = 0;
            SuccessfulCalls = 0;
            AverageResponseTime = 0;
        }

        /// <summary>
        /// 获取当前使用的密钥
        /// </summary>
        public string? GetCurrentKey()
        {
            if (KeyValues == null || KeyValues.Count == 0)
                return null;

            if (CurrentKeyIndex >= KeyValues.Count)
                CurrentKeyIndex = 0;

            return KeyValues[CurrentKeyIndex];
        }

        /// <summary>
        /// 切换到下一个密钥
        /// </summary>
        public void SwitchToNextKey()
        {
            if (KeyValues == null || KeyValues.Count <= 1)
                return;

            CurrentKeyIndex = (CurrentKeyIndex + 1) % KeyValues.Count;
        }

        /// <summary>
        /// 记录成功调用
        /// </summary>
        public void RecordSuccessfulCall(double responseTime)
        {
            LastUsedTime = DateTime.UtcNow;
            TotalCalls++;
            SuccessfulCalls++;

            // 更新平均响应时间
            if (SuccessfulCalls == 1)
            {
                AverageResponseTime = responseTime;
            }
            else
            {
                AverageResponseTime = (AverageResponseTime * (SuccessfulCalls - 1) + responseTime) / SuccessfulCalls;
            }

            // 重置失败次数
            FailureCount = 0;
        }

        /// <summary>
        /// 记录失败调用
        /// </summary>
        public void RecordFailedCall()
        {
            TotalCalls++;
            FailureCount++;

            // 如果失败次数超过最大值，自动禁用
            if (FailureCount >= MaxFailures)
            {
                IsEnabled = false;
            }
        }

        /// <summary>
        /// 重置失败计数
        /// </summary>
        public void ResetFailureCount()
        {
            FailureCount = 0;
            if (!IsEnabled && FailureCount == 0)
            {
                IsEnabled = true;
            }
        }

        /// <summary>
        /// 记录健康检查
        /// </summary>
        public void RecordHealthCheck()
        {
            LastCheckedTime = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double GetSuccessRate()
        {
            if (TotalCalls == 0)
                return 0;

            return (double)SuccessfulCalls / TotalCalls * 100;
        }
    }
}
