using myaiservice.Entities;
using myaiservice.Services.DomainServices;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;
using Microsoft.AspNetCore.Authorization;
using System.ComponentModel.DataAnnotations;

namespace myaiservice.Services.ApplicationServices
{
    /// <summary>
    /// Token管理应用服务
    /// </summary>
    [Authorize]
    public class TokenManagementAppService : ApplicationService
    {
        private readonly IRepository<Token, Guid> _tokenRepository;
        private readonly TokenDomainService _tokenDomainService;

        public TokenManagementAppService(
            IRepository<Token, Guid> tokenRepository,
            TokenDomainService tokenDomainService)
        {
            _tokenRepository = tokenRepository;
            _tokenDomainService = tokenDomainService;
        }

        /// <summary>
        /// 创建Token
        /// </summary>
        public async Task<TokenDto> CreateTokenAsync(CreateTokenDto input)
        {
            var currentUserId = CurrentUser.Id.Value;
            
            var token = await _tokenDomainService.CreateTokenAsync(input.Name, currentUserId, input.Type);
            
            // 设置其他属性
            token.IsUnlimited = input.IsUnlimited;
            token.RemainingQuota = input.InitialQuota;
            token.RequestLimitPerMinute = input.RequestLimitPerMinute ?? 60;
            token.RequestLimitPerHour = input.RequestLimitPerHour ?? 3600;
            token.RequestLimitPerDay = input.RequestLimitPerDay ?? 86400;
            token.AllowedModels = input.AllowedModels;
            token.IpWhitelist = input.IpWhitelist;
            token.AllowedGroups = input.AllowedGroups;
            token.ExpiresAt = input.ExpiresAt;
            token.Remarks = input.Remarks;

            await _tokenRepository.InsertAsync(token);

            return ObjectMapper.Map<Token, TokenDto>(token);
        }

        /// <summary>
        /// 获取用户的Token列表
        /// </summary>
        public async Task<List<TokenDto>> GetUserTokensAsync()
        {
            var currentUserId = CurrentUser.Id.Value;
            var tokens = await _tokenDomainService.GetUserTokensAsync(currentUserId);

            return ObjectMapper.Map<List<Token>, List<TokenDto>>(tokens);
        }

        /// <summary>
        /// 获取Token详情
        /// </summary>
        public async Task<TokenDto> GetTokenAsync(Guid id)
        {
            var token = await _tokenRepository.GetAsync(id);

            // 检查权限：只能查看自己的Token或管理员可以查看所有
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only view your own tokens");

            return ObjectMapper.Map<Token, TokenDto>(token);
        }

        /// <summary>
        /// 更新Token
        /// </summary>
        public async Task<TokenDto> UpdateTokenAsync(Guid id, UpdateTokenDto input)
        {
            var token = await _tokenRepository.GetAsync(id);

            // 检查权限：只能修改自己的Token或管理员可以修改所有
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only update your own tokens");

            token.Name = input.Name ?? token.Name;
            token.IsEnabled = input.IsEnabled ?? token.IsEnabled;
            token.IsUnlimited = input.IsUnlimited ?? token.IsUnlimited;
            token.RequestLimitPerMinute = input.RequestLimitPerMinute ?? token.RequestLimitPerMinute;
            token.RequestLimitPerHour = input.RequestLimitPerHour ?? token.RequestLimitPerHour;
            token.RequestLimitPerDay = input.RequestLimitPerDay ?? token.RequestLimitPerDay;
            token.AllowedModels = input.AllowedModels ?? token.AllowedModels;
            token.IpWhitelist = input.IpWhitelist ?? token.IpWhitelist;
            token.AllowedGroups = input.AllowedGroups ?? token.AllowedGroups;
            token.ExpiresAt = input.ExpiresAt ?? token.ExpiresAt;
            token.Remarks = input.Remarks ?? token.Remarks;

            await _tokenRepository.UpdateAsync(token);

            return ObjectMapper.Map<Token, TokenDto>(token);
        }

        /// <summary>
        /// 删除Token
        /// </summary>
        public async Task DeleteTokenAsync(Guid id)
        {
            var token = await _tokenRepository.GetAsync(id);
            
            // 检查权限：只能删除自己的Token或管理员可以删除所有
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only delete your own tokens");

            await _tokenRepository.DeleteAsync(id);
        }

        /// <summary>
        /// 启用/禁用Token
        /// </summary>
        public async Task SetTokenStatusAsync(Guid id, bool isEnabled)
        {
            var token = await _tokenRepository.GetAsync(id);

            // 检查权限
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only modify your own tokens");

            await _tokenDomainService.SetTokenStatusAsync(id, isEnabled);
        }

        /// <summary>
        /// 为Token添加配额
        /// </summary>
        public async Task AddTokenQuotaAsync(Guid id, decimal amount)
        {
            var token = await _tokenRepository.GetAsync(id);
            
            // 检查权限：只有管理员可以添加配额
            if (!CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("Only administrators can add quota");

            await _tokenDomainService.AddQuotaAsync(id, amount);
        }

        /// <summary>
        /// 重置Token统计
        /// </summary>
        public async Task ResetTokenStatisticsAsync(Guid id)
        {
            var token = await _tokenRepository.GetAsync(id);
            
            // 检查权限
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only reset your own token statistics");

            await _tokenDomainService.ResetTokenStatisticsAsync(id);
        }

        /// <summary>
        /// 获取Token统计信息
        /// </summary>
        public async Task<TokenStatisticsDto> GetTokenStatisticsAsync(Guid id)
        {
            var token = await _tokenRepository.GetAsync(id);

            // 检查权限
            if (token.UserId != CurrentUser.Id.Value && !CurrentUser.IsInRole("admin"))
                throw new UnauthorizedAccessException("You can only view your own token statistics");

            var statistics = await _tokenDomainService.GetTokenStatisticsAsync(id);
            return ObjectMapper.Map<TokenStatistics, TokenStatisticsDto>(statistics);
        }

        /// <summary>
        /// 验证Token (内部API)
        /// </summary>
        [AllowAnonymous]
        public async Task<TokenValidationResult> ValidateTokenAsync(string tokenValue, string? ipAddress = null, string? modelName = null)
        {
            var (isValid, errorMessage) = await _tokenDomainService.ValidateTokenAsync(tokenValue, ipAddress, modelName);
            
            if (!isValid)
            {
                return new TokenValidationResult
                {
                    IsValid = false,
                    ErrorMessage = errorMessage
                };
            }

            var token = await _tokenRepository.FirstOrDefaultAsync(t => t.Value == tokenValue);
            if (token == null)
            {
                return new TokenValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Token not found"
                };
            }

            return new TokenValidationResult
            {
                IsValid = true,
                TokenId = token.Id,
                UserId = token.UserId,
                TokenType = token.Type,
                IsUnlimited = token.IsUnlimited,
                RemainingQuota = token.RemainingQuota
            };
        }
    }

    // DTOs
    public class CreateTokenDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        public TokenType Type { get; set; } = TokenType.ApiAccess;

        public bool IsUnlimited { get; set; } = false;

        public decimal InitialQuota { get; set; } = 0;

        public int? RequestLimitPerMinute { get; set; }

        public int? RequestLimitPerHour { get; set; }

        public int? RequestLimitPerDay { get; set; }

        public string? AllowedModels { get; set; }

        public string? IpWhitelist { get; set; }

        public string? AllowedGroups { get; set; }

        public DateTime? ExpiresAt { get; set; }

        public string? Remarks { get; set; }
    }

    public class UpdateTokenDto
    {
        public string? Name { get; set; }

        public bool? IsEnabled { get; set; }

        public bool? IsUnlimited { get; set; }

        public int? RequestLimitPerMinute { get; set; }

        public int? RequestLimitPerHour { get; set; }

        public int? RequestLimitPerDay { get; set; }

        public string? AllowedModels { get; set; }

        public string? IpWhitelist { get; set; }

        public string? AllowedGroups { get; set; }

        public DateTime? ExpiresAt { get; set; }

        public string? Remarks { get; set; }
    }

    public class TokenDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public TokenType Type { get; set; }
        public Guid UserId { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsUnlimited { get; set; }
        public decimal RemainingQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public int RequestLimitPerMinute { get; set; }
        public int RequestLimitPerHour { get; set; }
        public int RequestLimitPerDay { get; set; }
        public string? AllowedModels { get; set; }
        public string? IpWhitelist { get; set; }
        public string? AllowedGroups { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public DateTime? LastUsedTime { get; set; }
        public string? LastUsedIp { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long TotalTokens { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public string? Remarks { get; set; }
        public DateTime CreationTime { get; set; }
    }

    public class TokenStatisticsDto
    {
        public Guid TokenId { get; set; }
        public long TotalRequests { get; set; }
        public long SuccessfulRequests { get; set; }
        public double SuccessRate { get; set; }
        public long TotalTokens { get; set; }
        public long InputTokens { get; set; }
        public long OutputTokens { get; set; }
        public decimal RemainingQuota { get; set; }
        public decimal UsedQuota { get; set; }
        public DateTime? LastUsedTime { get; set; }
    }

    public class TokenValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public Guid? TokenId { get; set; }
        public Guid? UserId { get; set; }
        public TokenType? TokenType { get; set; }
        public bool IsUnlimited { get; set; }
        public decimal RemainingQuota { get; set; }
    }
}
