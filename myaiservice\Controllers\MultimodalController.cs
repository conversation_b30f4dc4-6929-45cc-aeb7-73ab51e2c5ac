using Microsoft.AspNetCore.Mvc;
using myaiservice.Services.DomainServices;
using myaiservice.Services.ApplicationServices;
using Volo.Abp.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace myaiservice.Controllers
{
    /// <summary>
    /// 多模态API控制器 - 处理图像、音频等多媒体请求
    /// </summary>
    [ApiController]
    [Route("v1")]
    [AllowAnonymous]
    public class MultimodalController : AbpControllerBase
    {
        private readonly MultimodalService _multimodalService;
        private readonly ModelConfigurationService _modelConfigurationService;
        private readonly TokenManagementAppService _tokenManagementAppService;

        public MultimodalController(
            MultimodalService multimodalService,
            ModelConfigurationService modelConfigurationService,
            TokenManagementAppService tokenManagementAppService)
        {
            _multimodalService = multimodalService;
            _modelConfigurationService = modelConfigurationService;
            _tokenManagementAppService = tokenManagementAppService;
        }

        /// <summary>
        /// 视觉聊天接口 - 支持图像输入的聊天
        /// </summary>
        [HttpPost("chat/completions/vision")]
        public async Task<IActionResult> VisionChatAsync([FromForm] VisionChatRequest request)
        {
            try
            {
                // 1. 验证Token
                var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new { error = "Missing or invalid authorization header" });
                }

                var tokenValue = authHeader["Bearer ".Length..].Trim();
                var tokenValidation = await _tokenManagementAppService.ValidateTokenAsync(tokenValue, null, request.Model);
                if (!tokenValidation.IsValid)
                {
                    return Unauthorized(new { error = tokenValidation.ErrorMessage });
                }

                // 2. 检查模型是否支持视觉
                var supportsVision = await _multimodalService.CheckModelSupportsFeatureAsync(request.Model, Entities.ModelFeature.Vision);
                if (!supportsVision)
                {
                    return BadRequest(new { error = $"Model '{request.Model}' does not support vision" });
                }

                // 3. 处理图像输入
                var processedImages = new List<ProcessedImageInput>();
                if (request.Images != null && request.Images.Count > 0)
                {
                    foreach (var image in request.Images)
                    {
                        var processedImage = await _multimodalService.ProcessImageInputAsync(request.Model, image);
                        processedImages.Add(processedImage);
                    }
                }

                // 4. 构建多模态消息
                var multimodalMessage = await _multimodalService.BuildMultimodalMessageAsync(
                    request.Model, 
                    request.Message, 
                    processedImages);

                // 5. 估算Token消耗
                var estimatedUsage = await _multimodalService.EstimateMultimodalTokenUsageAsync(
                    request.Model, 
                    request.Message, 
                    processedImages);

                return Ok(new
                {
                    message = "Vision chat request processed",
                    model = request.Model,
                    estimated_tokens = estimatedUsage.TotalTokens,
                    image_count = processedImages.Count,
                    multimodal_message = multimodalMessage
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 音频转录接口 (Whisper兼容)
        /// </summary>
        [HttpPost("audio/transcriptions")]
        public async Task<IActionResult> AudioTranscriptionsAsync([FromForm] AudioTranscriptionRequest request)
        {
            try
            {
                // 1. 验证Token
                var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new { error = "Missing or invalid authorization header" });
                }

                var tokenValue = authHeader["Bearer ".Length..].Trim();
                var tokenValidation = await _tokenManagementAppService.ValidateTokenAsync(tokenValue, null, request.Model);
                if (!tokenValidation.IsValid)
                {
                    return Unauthorized(new { error = tokenValidation.ErrorMessage });
                }

                // 2. 检查模型是否支持音频
                var supportsAudio = await _multimodalService.CheckModelSupportsFeatureAsync(request.Model, Entities.ModelFeature.Audio);
                if (!supportsAudio)
                {
                    return BadRequest(new { error = $"Model '{request.Model}' does not support audio transcription" });
                }

                // 3. 处理音频输入
                var processedAudio = await _multimodalService.ProcessAudioInputAsync(request.Model, request.File);

                // 4. 估算Token消耗
                var estimatedUsage = await _multimodalService.EstimateMultimodalTokenUsageAsync(
                    request.Model, 
                    string.Empty, 
                    null, 
                    processedAudio);

                // TODO: 实际调用音频转录API
                var transcriptionResult = new
                {
                    text = "This is a placeholder transcription result.",
                    language = request.Language ?? "en",
                    duration = processedAudio.Duration.TotalSeconds,
                    segments = new[]
                    {
                        new
                        {
                            id = 0,
                            seek = 0,
                            start = 0.0,
                            end = processedAudio.Duration.TotalSeconds,
                            text = "This is a placeholder transcription result.",
                            tokens = new[] { 1, 2, 3, 4, 5 },
                            temperature = 0.0,
                            avg_logprob = -0.5,
                            compression_ratio = 1.0,
                            no_speech_prob = 0.1
                        }
                    }
                };

                return Ok(transcriptionResult);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 语音合成接口 (TTS)
        /// </summary>
        [HttpPost("audio/speech")]
        public async Task<IActionResult> TextToSpeechAsync([FromBody] TextToSpeechRequest request)
        {
            try
            {
                // 1. 验证Token
                var authHeader = HttpContext.Request.Headers.Authorization.FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new { error = "Missing or invalid authorization header" });
                }

                var tokenValue = authHeader["Bearer ".Length..].Trim();
                var tokenValidation = await _tokenManagementAppService.ValidateTokenAsync(tokenValue, null, request.Model);
                if (!tokenValidation.IsValid)
                {
                    return Unauthorized(new { error = tokenValidation.ErrorMessage });
                }

                // 2. 检查模型类型
                var modelConfig = await _modelConfigurationService.GetModelConfigurationAsync(request.Model);
                if (modelConfig.Type != Entities.ModelType.TextToSpeech)
                {
                    return BadRequest(new { error = $"Model '{request.Model}' is not a text-to-speech model" });
                }

                // TODO: 实际调用TTS API
                // 这里返回一个占位符响应
                return Ok(new
                {
                    message = "Text-to-speech request processed",
                    model = request.Model,
                    input = request.Input,
                    voice = request.Voice,
                    response_format = request.ResponseFormat,
                    speed = request.Speed
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 获取模型能力信息
        /// </summary>
        [HttpGet("models/{modelName}/capabilities")]
        public async Task<IActionResult> GetModelCapabilitiesAsync(string modelName)
        {
            try
            {
                var capabilities = await _modelConfigurationService.GetModelCapabilitiesAsync(modelName);
                return Ok(capabilities);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 获取模型参数限制
        /// </summary>
        [HttpGet("models/{modelName}/parameters")]
        public async Task<IActionResult> GetModelParametersAsync(string modelName)
        {
            try
            {
                var parameters = await _modelConfigurationService.GetModelParameterLimitsAsync(modelName);
                return Ok(parameters);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }

    // Request DTOs
    public class VisionChatRequest
    {
        public string Model { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public List<IFormFile>? Images { get; set; }
        public double? Temperature { get; set; }
        public int? MaxTokens { get; set; }
    }

    public class AudioTranscriptionRequest
    {
        public IFormFile File { get; set; } = null!;
        public string Model { get; set; } = string.Empty;
        public string? Language { get; set; }
        public string? Prompt { get; set; }
        public string? ResponseFormat { get; set; } = "json";
        public double? Temperature { get; set; } = 0;
    }

    public class TextToSpeechRequest
    {
        public string Model { get; set; } = string.Empty;
        public string Input { get; set; } = string.Empty;
        public string Voice { get; set; } = "alloy";
        public string? ResponseFormat { get; set; } = "mp3";
        public double? Speed { get; set; } = 1.0;
    }
}
